[package]
name = "appcove-malawi"
version = "0.1.0"
edition = "2024"

[package.metadata.acp]
module = {}
extends = ["appcove-zero", "approck", "bux", "granite", "auth-fence"]


[dependencies]
approck = {workspace = true}
bux = { workspace = true }
granite = { workspace = true }
auth-fence = { workspace = true }
approck-redis = { workspace = true }
approck-postgres = { workspace = true }

maud = { workspace = true }

appcove-zero = { path = "../appcove-zero" }
