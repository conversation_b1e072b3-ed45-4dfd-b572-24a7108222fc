import "./add.mcss";
import "@bux/input/select/nilla.mts";
import "@bux/input/datetime.mts";
import "@bux/input/textarea/string.mts";
import "@bux/input/checkbox.mts";

import { SE } from "@granite/lib.mts";
import { schedule_add } from "./addλ.mts";
import { go_back, go_next } from "@bux/singleton/nav_stack.mts";

import BuxInputSelectNilla from "@bux/input/select/nilla.mts";
import BuxInputDatetime from "@bux/input/datetime.mts";
import BuxInputTextareaString from "@bux/input/textarea/string.mts";
import FormPanel from "@bux/component/form_panel.mts";

const $form = SE(document, "form.form-panel") as HTMLFormElement;
const $guard_uuid: BuxInputSelectNilla<string> = SE($form, "[name=guard_uuid]");
const $start_time: BuxInputDatetime = SE($form, "[name=start_ts]");
const $end_time: BuxInputDatetime = SE($form, "[name=end_ts]");
const $note: BuxInputTextareaString = SE($form, "[name=note]");

new FormPanel({
    $form,
    api: schedule_add.api,
    on_cancel: go_back,

    err: (errors) => {
        $guard_uuid.set_e(errors.guard_uuid);
        $start_time.set_e(errors.start_ts);
        $end_time.set_e(errors.end_ts);
        $note.set_e(errors.note);
    },

    get: () => {
        return {
            guard_uuid: $guard_uuid.value,
            start_ts: $start_time.value,
            end_ts: $end_time.value,
            note: $note.value_option,
        };
    },

    set: (_value) => {
    },

    out: (output) => {
        go_next(output.detail_url);
    },
});
