use crate::{AppStruct, IdentityStruct};
use std::ops::{Deref, DerefMut};

impl auth_fence::Identity for IdentityStruct {
    fn is_logged_in(&self) -> bool {
        self.auth_fence.is_some()
    }
    fn identity_uuid(&self) -> Option<granite::Uuid> {
        self.auth_fence
            .as_ref()
            .map(|auth_fence| auth_fence.identity_uuid)
    }
    fn remote_address(&self) -> std::net::IpAddr {
        self.request.remote_address
    }
    fn session_token(&self) -> String {
        self.request.session_token.clone()
    }
}

impl auth_fence::App for AppStruct {
    fn after_login_next_url<'a>(&self, next_uri: Option<&'a str>) -> &'a str {
        match next_uri {
            Some(next_uri) => next_uri,
            None => "/dashboard/",
        }
    }
    fn auth_fence_system(&self) -> &auth_fence::types::ModuleStruct {
        &self.auth_fence
    }
}

#[derive(Debug)]
pub struct ModuleStruct(auth_fence::types::ModuleStruct);

impl Deref for ModuleStruct {
    type Target = auth_fence::types::ModuleStruct;

    fn deref(&self) -> &Self::Target {
        &self.0
    }
}

impl DerefMut for ModuleStruct {
    fn deref_mut(&mut self) -> &mut Self::Target {
        &mut self.0
    }
}

impl approck::Module for ModuleStruct {
    type Config = auth_fence::types::ModuleConfig;
    fn new(config: Self::Config) -> granite::Result<Self> {
        // Create basic auth fence system with Google OAuth support
        let system = auth_fence::types::ModuleStruct::new(config)?;
        Ok(ModuleStruct(system))
    }

    async fn init(&self) -> granite::Result<()> {
        Ok(())
    }
}
