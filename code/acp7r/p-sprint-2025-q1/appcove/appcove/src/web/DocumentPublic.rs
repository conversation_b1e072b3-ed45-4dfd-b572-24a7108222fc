bux::document! {
    pub struct DocumentPublic {}

    impl DocumentPublic {
        pub fn new(
            _app: &'static crate::AppStruct,
            identity: &crate::IdentityStruct,
            req: &approck::server::Request,
        ) -> Self {
            // trait Nav2 must be in scope for set_identity() and nav2_menu_add()
            use bux::document::{Base, Nav2};

            let mut this = Self {
                ..Default::default()
            };

            // Base setup
            this.set_uri(req.path());
            this.set_title("AppCove"); // default title

            // Nav2 setup
            this.set_identity(identity);

            this
        }
    }

    impl bux::document::Base for DocumentPublic {
        fn render_body(&self) -> maud::Markup {
            use bux::document::Base;
            use maud::html;
            html!(
                (Base::render_body_inner(self))
            )
        }
    }
    impl auth_fence::DocumentPublic for DocumentPublic {}
    impl appcove_public::DocumentPublic for DocumentPublic {}
    impl bux::document::Nav1 for DocumentPublic {}
    impl bux::document::Nav2 for DocumentPublic {}
    impl bux::document::FooterLinks for DocumentPublic {}
    impl auth_fence::Document for DocumentPublic {}
    impl bux::document::PageNav for DocumentPublic {}
}
