use std::{pin::Pin, task::Poll};

use futures::TryStreamExt;
use headers::HeaderMapExt;
use http::header::InvalidHeaderValue;
use http_body_util::BodyExt;

use super::Frame;

pub type Result = granite::Result<Response>;

#[derive(Default)]
pub struct Raw {
    pub content: bytes::Bytes,
    pub status: http::StatusCode,
    pub headers: http::HeaderMap,
}
impl Raw {
    pub fn new() -> Self {
        Self { ..Self::default() }
    }
}
impl From<Raw> for hyper::Response<HyperResponseBody> {
    fn from(value: Raw) -> Self {
        let mut response = hyper::Response::new(HyperResponseBody::Bytes(value.content));
        response.headers_mut().extend(value.headers);
        *response.status_mut() = value.status;
        response
    }
}

macro_rules! bytes_response {
    ($t:ident, $mime:expr, $content_type:ty) => {
        #[derive(Default)]
        pub struct $t {
            pub content: $content_type,
            pub status: http::StatusCode,
            pub headers: http::HeaderMap,
        }
        impl $t {
            pub fn new(content: impl Into<$content_type>) -> Self {
                Self {
                    content: content.into(),
                    ..Self::default()
                }
            }
            pub fn new_with_status(
                content: impl Into<$content_type>,
                status: http::StatusCode,
            ) -> Self {
                Self {
                    content: content.into(),
                    status,
                    ..Self::default()
                }
            }

            pub fn new_with_status_and_header_map(
                content: impl Into<$content_type>,
                status: http::StatusCode,
                headers: http::HeaderMap,
            ) -> Self {
                Self {
                    content: content.into(),
                    status,
                    headers,
                }
            }
        }
        impl From<$t> for hyper::Response<HyperResponseBody> {
            fn from(value: $t) -> Self {
                let mut response =
                    hyper::Response::new(HyperResponseBody::Bytes(value.content.into()));
                let headers = response.headers_mut();
                headers.typed_insert(headers::ContentType::from($mime));
                headers.extend(value.headers);
                *response.status_mut() = value.status;
                response
            }
        }
    };
}

bytes_response!(Bytes, mime::APPLICATION_OCTET_STREAM, bytes::Bytes);

pub struct Stream {
    pub content: futures::stream::BoxStream<'static, granite::Result<super::Frame>>,
    pub status: http::StatusCode,
    pub headers: http::HeaderMap,
}

impl Stream {
    pub fn new<S>(stream: S) -> Self
    where
        S: futures::Stream<Item = granite::Result<crate::server::Frame>> + Send + 'static,
    {
        Self {
            content: Box::pin(stream),
            status: http::StatusCode::default(),
            headers: http::HeaderMap::default(),
        }
    }
}

impl<S, D> From<S> for Stream
where
    S: futures::Stream<Item = granite::Result<D>> + Send + 'static,
    D: Into<bytes::Bytes>,
{
    fn from(stream: S) -> Self {
        Self::new(stream.map_ok(|data| Frame::Data(data.into())))
    }
}

impl From<Stream> for hyper::Response<HyperResponseBody> {
    fn from(val: Stream) -> Self {
        let mut response =
            hyper::Response::new(HyperResponseBody::Stream(http_body_util::StreamBody::new(
                Box::pin(val.content.map_err(granite::StdError::from).map_ok(
                    |frame| match frame {
                        super::Frame::Data(buf) => hyper::body::Frame::data(buf),
                        super::Frame::Trailers(map) => hyper::body::Frame::trailers(map),
                    },
                )),
            )));
        let headers = response.headers_mut();
        headers.typed_insert(headers::ContentType::from(mime::APPLICATION_OCTET_STREAM));
        headers.extend(val.headers);
        *response.status_mut() = val.status;
        response
    }
}

bytes_response!(Text, mime::TEXT_PLAIN_UTF_8, String);

impl From<&str> for Text {
    fn from(content: &str) -> Self {
        Self::new(content.to_string())
    }
}

impl From<String> for Text {
    fn from(content: String) -> Self {
        Self::new(content)
    }
}

pub struct Empty {
    pub status: http::StatusCode,
    pub headers: http::HeaderMap,
}

impl From<Empty> for hyper::Response<HyperResponseBody> {
    fn from(val: Empty) -> Self {
        let mut response = hyper::Response::new(HyperResponseBody::Empty);
        let headers = response.headers_mut();
        headers.extend(val.headers);
        *response.status_mut() = val.status;
        response
    }
}

impl Default for Empty {
    fn default() -> Self {
        Self {
            status: http::StatusCode::OK,
            headers: http::HeaderMap::new(),
        }
    }
}

bytes_response!(HTML, mime::TEXT_HTML_UTF_8, String);

impl From<&str> for HTML {
    fn from(content: &str) -> Self {
        Self::new(content.to_string())
    }
}

impl From<String> for HTML {
    fn from(content: String) -> Self {
        Self::new(content)
    }
}

bytes_response!(JavaScript, mime::TEXT_JAVASCRIPT, String);

impl From<&str> for JavaScript {
    fn from(content: &str) -> Self {
        Self::new(content.to_string())
    }
}

bytes_response!(CSS, mime::TEXT_CSS, String);

impl From<&str> for CSS {
    fn from(content: &str) -> Self {
        Self::new(content.to_string())
    }
}

bytes_response!(JSON, mime::APPLICATION_JSON, String);

impl From<&str> for JSON {
    fn from(content: &str) -> Self {
        Self::new(content.to_string())
    }
}

impl From<String> for JSON {
    fn from(content: String) -> Self {
        Self::new(content)
    }
}

impl From<serde_json::Value> for JSON {
    fn from(content: serde_json::Value) -> Self {
        Self::new(content.to_string())
    }
}

bytes_response!(SVG, mime::IMAGE_SVG, String);

impl From<&str> for SVG {
    fn from(content: &str) -> Self {
        Self::new(content.to_string())
    }
}

pub struct NotFound;

impl Default for NotFound {
    fn default() -> Self {
        Self
    }
}

impl From<NotFound> for hyper::Response<HyperResponseBody> {
    fn from(_: NotFound) -> Self {
        let mut response = hyper::Response::new(HyperResponseBody::Bytes("Not Found".into()));
        let headers = response.headers_mut();
        headers.typed_insert(headers::ContentType::from(mime::TEXT_PLAIN_UTF_8));
        *response.status_mut() = http::StatusCode::NOT_FOUND;
        response
    }
}

#[derive(Debug)]
pub struct Redirect {
    pub location: String,
    pub status: http::StatusCode,
}

impl Redirect {
    pub fn see_other(location: String) -> Self {
        Self {
            location,
            status: http::StatusCode::SEE_OTHER,
        }
    }

    pub fn temporary(location: String) -> Self {
        Self {
            location,
            status: http::StatusCode::TEMPORARY_REDIRECT,
        }
    }

    pub fn permanent(location: String) -> Self {
        Self {
            location,
            status: http::StatusCode::PERMANENT_REDIRECT,
        }
    }
}

impl From<Redirect> for hyper::Response<HyperResponseBody> {
    fn from(val: Redirect) -> Self {
        let mut response = hyper::Response::new(HyperResponseBody::Empty);
        let headers = response.headers_mut();
        headers.insert(
            http::header::LOCATION,
            http::header::HeaderValue::from_str(&val.location).unwrap(),
        );
        *response.status_mut() = val.status;
        response
    }
}

impl From<&str> for Redirect {
    fn from(location: &str) -> Self {
        Self::see_other(location.to_string())
    }
}

pub struct WebSocketUpgrade(hyper::Response<http_body_util::Full<bytes::Bytes>>);

impl From<hyper::Response<http_body_util::Full<bytes::Bytes>>> for WebSocketUpgrade {
    fn from(value: hyper::Response<http_body_util::Full<bytes::Bytes>>) -> Self {
        Self(value)
    }
}

// This can be made more efficient in the future by adding an extra variant to HyperResponseBody
// for Full to avoid converting it into the Bytes response.
impl From<WebSocketUpgrade> for hyper::Response<HyperResponseBody> {
    fn from(val: WebSocketUpgrade) -> Self {
        let (parts, body) = val.0.into_parts();
        let data = futures::executor::block_on(body.collect())
            .expect("Collecting bytes on a full body should not fail");
        let new_body = HyperResponseBody::Bytes(data.to_bytes());
        hyper::Response::from_parts(parts, new_body)
    }
}

/// Implements [`hyper::body::Body`] trait
pub(crate) enum HyperResponseBody {
    Empty,
    Bytes(bytes::Bytes),
    Stream(
        http_body_util::StreamBody<
            futures::stream::BoxStream<
                'static,
                std::result::Result<hyper::body::Frame<bytes::Bytes>, granite::StdError>,
            >,
        >,
    ),
}

impl hyper::body::Body for HyperResponseBody {
    type Data = bytes::Bytes;
    type Error = granite::StdError;

    fn poll_frame(
        self: std::pin::Pin<&mut Self>,
        cx: &mut std::task::Context<'_>,
    ) -> Poll<Option<std::result::Result<hyper::body::Frame<Self::Data>, Self::Error>>> {
        match self.get_mut() {
            HyperResponseBody::Empty => Poll::Ready(None),
            HyperResponseBody::Bytes(body) => {
                if body.is_empty() {
                    Poll::Ready(None)
                } else {
                    let data = std::mem::replace(body, bytes::Bytes::new());
                    Poll::Ready(Some(Ok(hyper::body::Frame::data(data))))
                }
            }
            HyperResponseBody::Stream(stream) => {
                hyper::body::Body::poll_frame(Pin::new(stream), cx)
            }
        }
    }

    fn size_hint(&self) -> hyper::body::SizeHint {
        match self {
            HyperResponseBody::Empty => hyper::body::SizeHint::with_exact(0),
            HyperResponseBody::Bytes(body) => match u64::try_from(body.len()) {
                Ok(size) => hyper::body::SizeHint::with_exact(size),
                Err(_) => hyper::body::SizeHint::default(),
            },
            HyperResponseBody::Stream(stream) => stream.size_hint(),
        }
    }

    fn is_end_stream(&self) -> bool {
        match self {
            HyperResponseBody::Empty => true,
            HyperResponseBody::Bytes(body) => body.is_empty(),
            HyperResponseBody::Stream(stream) => stream.is_end_stream(),
        }
    }
}

pub enum Response {
    Raw(Raw),
    Bytes(Bytes),
    Stream(Stream),
    Text(Text),
    Empty(Empty),
    HTML(HTML),
    JavaScript(JavaScript),
    CSS(CSS),
    JSON(JSON),
    SVG(SVG),
    NotFound(NotFound),
    Redirect(Redirect),
    WebSocketUpgrade(WebSocketUpgrade),
}

impl From<Response> for hyper::Response<HyperResponseBody> {
    fn from(val: Response) -> Self {
        match val {
            Response::Raw(raw) => raw.into(),
            Response::Bytes(bytes) => bytes.into(),
            Response::Stream(stream) => stream.into(),
            Response::Text(text) => text.into(),
            Response::Empty(empty) => empty.into(),
            Response::HTML(html) => html.into(),
            Response::JavaScript(javascript) => javascript.into(),
            Response::CSS(css) => css.into(),
            Response::JSON(json) => json.into(),
            Response::SVG(svg) => svg.into(),
            Response::NotFound(not_found) => not_found.into(),
            Response::Redirect(redirect) => redirect.into(),
            Response::WebSocketUpgrade(websocket_upgrade) => websocket_upgrade.into(),
        }
    }
}

impl From<granite::HTTPResponse> for Response {
    fn from(value: granite::HTTPResponse) -> Self {
        Self::Raw(Raw {
            content: value.body.into(),
            status: value.status,
            headers: value.headers,
        })
    }
}

pub fn not_found() -> Response {
    Response::NotFound(NotFound)
}

pub fn json(content: impl Into<String>) -> Response {
    Response::JSON(JSON::from(content.into()))
}

pub fn javascript(content: &str) -> Response {
    Response::JavaScript(JavaScript::from(content))
}

pub fn css(content: &str) -> Response {
    Response::CSS(CSS::from(content))
}

pub fn bytes(content: impl Into<bytes::Bytes>) -> Response {
    Response::Bytes(Bytes::new(content))
}

pub fn bytes_content_type_long_cache(
    content: impl Into<bytes::Bytes>,
    content_type: &str,
) -> std::result::Result<Response, InvalidHeaderValue> {
    Ok(Response::Raw(Raw {
        content: content.into(),
        status: http::StatusCode::OK,
        headers: http::HeaderMap::from_iter([
            (
                http::header::CONTENT_TYPE,
                http::HeaderValue::from_str(content_type)?,
            ),
            (
                http::header::CACHE_CONTROL,
                http::HeaderValue::from_static("public, max-age=31536000"),
            ),
        ]),
    }))
}
