/*
Examples of Redis Session token key and object structure:
---------------------------------------------------------
Redis key:
    /AppSession/20240411175445c44224ff853098adf0ef3f376a7ffbb6ddad2213803c47743f/AuthStack
Redis object:
    Public session:
    {
        "AuthTime": 1713896812.4246874,
        "RefreshTime": 1713896818.4582505,
        "Stack": [
            {
                "Key": ["Public"],
                "PreviousURI": null,
                "AdminMode": false,
                "WriteMode": false,
                "SuperMode": false,
                "DebugMode": false,
                "LockDownType": null,
                "AdminPerm": false,
                "SuperPerm": false,
                "DebugPerm": false,
                "Name": "Public",
            }
        ]
    }

    Private session:
    {
        "AuthTime": 1713296017.1831234,
        "RefreshTime": 1713301827.9128642,a
        "Stack": [
            {
                "Key": ["ACRM_Contact", 110076, null],
                "PreviousURI": null,
                "AdminMode": true,
                "WriteMode": false,
                "SuperMode": false,
                "DebugMode": false,
                "LockDownType": null,
                "Contact_GSID": "JEVeJj95To9T0Kvo",
                "Contact_ZNID": ********,
                "Name": "James Garber",
                "Email": "<EMAIL>",
                "AdminPerm": true,
                "SuperPerm": false,
                "DebugPerm": false,
                "TagCache": [1, 3109, ********, ********],
                "TimeZone": "US/Eastern",
                "Account_GSID": null,
                "Account_Name": null,
                "Contact_AccountManagementPerm": null,
                "Account_List": [],
                "Account_ZNID": null,
                "ProfilePictureURI": "https://smartadvisortools-com.local.acp7.net/AppStruct/DefaultThumbnail.png"
            }
        ]
    }
*/

use approck_redis::RedisCX;
use appstruct::get_session_key;
use serde::de::{self, Deserializer, SeqAccess, Visitor};
use serde::{Deserialize, Serialize};
use std::fmt;

#[derive(Debug, Serialize, Deserialize)]
pub enum KeyType {
    None,
    Public,
    #[serde(rename = "ACRM_Contact")]
    ACRMContact,
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename = "Contact_ZNID")]
pub struct ContactZNID(u32);

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename = "Account_ZNID")]
pub struct AccountZNID(u32);

#[derive(Debug, Serialize)]
pub enum Key {
    Public,
    #[serde(rename = "ACRM_Contact")]
    ACRMContact(ContactZNID, Option<AccountZNID>),
}

struct KeyVisitor;

impl<'de> Visitor<'de> for KeyVisitor {
    type Value = Key;

    fn expecting(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
        formatter.write_str("a string or a sequence")
    }

    fn visit_seq<A>(self, mut seq: A) -> Result<Self::Value, A::Error>
    where
        A: SeqAccess<'de>,
    {
        /*
        let tag: KeyType = match seq.next_element() {
            Ok(value) => match value {
                Some(value) => value,
                None => KeyType::None,
            },
            Err(_) => KeyType::None,
        };
         */

        match seq.next_element()? {
            Some(KeyType::Public) => Ok(Key::Public),
            Some(KeyType::ACRMContact) => match seq.next_element()? {
                Some(id) => {
                    let account_mnid: Option<AccountZNID> = seq.next_element().unwrap_or_default();
                    Ok(Key::ACRMContact(id, account_mnid))
                }
                None => Err(de::Error::custom("expected a 'Contact_MNID'")),
            },
            _ => Err(de::Error::custom(
                "expected 'Public' or 'ACRM_Contact' key for session",
            )),
        }
    }
}

impl<'de> Deserialize<'de> for Key {
    fn deserialize<D>(deserializer: D) -> Result<Self, D::Error>
    where
        D: Deserializer<'de>,
    {
        deserializer.deserialize_any(KeyVisitor)
    }
}

#[derive(Debug, Serialize, Deserialize)]
pub struct User {
    #[serde(rename = "Key")]
    pub key: Key,
    #[serde(rename = "PreviousURI")]
    pub previous_uri: Option<String>,
    #[serde(rename = "AdminPerm")]
    pub admin_perm: bool,
    #[serde(rename = "AdminMode")]
    pub admin_mode: bool,
    #[serde(rename = "WriteMode")]
    pub write_mode: bool,
    #[serde(rename = "SuperPerm")]
    pub super_perm: bool,
    #[serde(rename = "SuperMode")]
    pub super_mode: bool,
    #[serde(rename = "DebugPerm")]
    pub debug_perm: bool,
    #[serde(rename = "DebugMode")]
    pub debug_mode: bool,
    #[serde(rename = "LockDownType")]
    pub lock_down_type: Option<String>,
    #[serde(rename = "Name")]
    pub name: String,
    #[serde(rename = "Email")]
    pub email: Option<String>,
    #[serde(rename = "TagCache")]
    pub tag_cache: Option<Vec<u32>>,
    #[serde(rename = "TimeZone")]
    pub time_zone: Option<String>,
    #[serde(rename = "ProfilePictureURI")]
    pub profile_picture_uri: Option<String>,
    #[serde(rename = "Contact_ZNID")]
    pub contact_znid: Option<u32>,
    #[serde(rename = "Contact_GSID")]
    pub contact_gsid: Option<String>,
    #[serde(rename = "Account_ZNID")]
    pub account_znid: Option<u32>,
    #[serde(rename = "Account_GSID")]
    pub account_gsid: Option<String>,
    #[serde(rename = "Account_Name")]
    pub account_name: Option<String>,
    #[serde(rename = "Contact_AccountManagementPerm")]
    pub account_management_perm: Option<bool>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AuthStack {
    #[serde(rename = "AuthTime")]
    pub auth_time: f64,
    #[serde(rename = "RefreshTime")]
    pub refresh_time: f64,
    #[serde(rename = "Stack")]
    pub stack: Vec<User>,
}

impl AuthStack {
    pub fn get_current_user(&self) -> Option<&User> {
        let user = self.stack.last();

        match user {
            Some(user) => Some(user),
            None => None,
        }
    }

    pub fn get_current_user_name(&self) -> String {
        match self.get_current_user() {
            Some(user) => user.name.clone(),
            None => "".to_string(),
        }
    }

    pub fn check_auth(&self) -> bool {
        match self.get_current_user() {
            Some(user) => match &user.key {
                Key::Public => false,
                Key::ACRMContact(_, _) => true,
            },
            None => false,
        }
    }

    pub fn check_tag(&self, tag: u32) -> bool {
        for user in self.stack.iter().rev() {
            if let Some(tag_cache) = &user.tag_cache {
                if tag_cache.contains(&tag) {
                    return true;
                }
            }
        }

        false
    }

    pub fn has_admin_perm(&self) -> bool {
        // get the first stack item
        let user = match self.stack.first() {
            Some(user) => user,
            None => return false,
        };

        user.admin_perm
    }

    pub fn has_admin_mode(&self) -> bool {
        // get the last stack item
        let user = match self.stack.last() {
            Some(user) => user,
            None => return false,
        };

        user.admin_mode
    }
}

pub async fn get_auth_info(
    redis: &mut RedisCX<'_>,
    session_token: &String,
) -> granite::Result<AuthStack> {
    let session_key = get_session_key(session_token);

    match redis.get_json::<AuthStack>(&session_key).await {
        Ok(auth_info) => Ok(auth_info),
        Err(e) => Err(e.add_context("Login Required")),
    }
}
