use std::io::Write;
use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use std::rc::Rc;
use std::path::{Path, PathBuf};
use syn::File as SynFile;
use serde::{Deserialize, Serialize};
use termcolor::WriteColor;

#[cfg(target_os = "linux")]
fn get_memory_usage() -> Option<usize> {
    use std::fs;
    let status = fs::read_to_string("/proc/self/status").ok()?;
    for line in status.lines() {
        if line.starts_with("VmRSS:") {
            let parts: Vec<&str> = line.split_whitespace().collect();
            if parts.len() >= 2 {
                return parts[1].parse::<usize>().ok();
            }
        }
    }
    None
}

#[cfg(not(target_os = "linux"))]
fn get_memory_usage() -> Option<usize> {
    None
}

pub trait Audit: Send + Sync {
    fn name(&self) -> &str;
    fn run(&self, context: &AuditContext) -> Vec<AuditFinding>;
    fn as_any(&self) -> &dyn std::any::Any;
}

#[derive(Debug, Deserialize, Serialize, Clone, PartialEq, Eq)]
#[derive(Default)]
pub enum AuditOutcome {
    #[default]
    Pass,
    Warn,
    Fail,
}


#[derive(Debug, Serialize)]
pub struct AuditReport {
    pub audit_name: String, // "dependency-synchronization", "file-existence check"
    pub findings: Vec<AuditFinding>,
    pub summary: AuditSummary, // Counts of pass/warn/fail for quick stats
}

// Example usage: An audit might return Vec<AuditFinding>
#[derive(Debug, Deserialize, Default,Serialize, Clone)]
pub struct AuditFinding {
    pub audit_outcome: AuditOutcome,
    pub message: String, // Human-readable description,
    pub file: Option<PathBuf>, // Path to the affected file,
    pub line: Option<usize>,
    pub suggestion: Option<String>,
}

impl AuditFinding {
    pub fn new(outcome: AuditOutcome, message: String, file: Option<PathBuf>) -> Self {
        Self {
            audit_outcome: outcome,
            message,
            file,
            line: None,
            suggestion: None,
        }
    }
}

#[derive(Debug, Serialize)]
pub struct AuditSummary {
    pub passes: usize,
    pub warnings: usize,
    pub failures: usize,
}

impl AuditSummary {
    pub fn from(findings: &[AuditFinding]) -> Self {
        let (passes, warnings, failures) = findings.iter().fold((0, 0, 0), |(p, w, f), finding| match finding.audit_outcome {
            AuditOutcome::Pass => (p + 1, w, f),
            AuditOutcome::Warn => (p, w + 1, f),
            AuditOutcome::Fail => (p, w, f + 1),
        });
        Self { passes, warnings, failures }
    }
}

pub struct AuditContext <'a> {
    pub workspace: &'a crate::Workspace,
    pub file_contents: Mutex<HashMap<PathBuf, Arc<String>>>,
    pub parsed_files: Mutex<HashMap<PathBuf, Option<Rc<SynFile>>>>,
}

impl<'a> AuditContext<'a> {
    pub fn new(workspace: &'a crate::Workspace) -> Self {
        Self {
            workspace,
            file_contents: Mutex::new(HashMap::new()),
            parsed_files: Mutex::new(HashMap::new()),
        }
    }

    pub fn get_file_content(&self, path: &Path) -> Option<Arc<String>> {
        let cache_result = self.file_contents.lock();
        match cache_result {
            Ok(mut cache) => {
                match cache.entry(path.to_path_buf()) {
                    std::collections::hash_map::Entry::Occupied(entry) => {
                        Some(entry.get().clone())
                    }
                    std::collections::hash_map::Entry::Vacant(entry) => {
                        let content = std::fs::read_to_string(path).ok()?;
                        Some(entry.insert(Arc::new(content)).clone())
                    }
                }
            }
            Err(_) => None,
        }
    }

    pub fn get_ast(&self, path: &Path) -> Option<Rc<SynFile>> {
        match self.parsed_files.lock() {
            Ok(mut cache) => {
                match cache.entry(path.to_path_buf()) {
                    std::collections::hash_map::Entry::Occupied(entry) => {
                        entry.get().clone()  
                    }
                    std::collections::hash_map::Entry::Vacant(entry) => {
                        if let Some(content) = self.get_file_content(path) {
                            match syn::parse_file(&content) {
                                Ok(ast) => {
                                    let arc_ast = Rc::new(ast);
                                    entry.insert(Some(arc_ast.clone()));  
                                    Some(arc_ast)  
                                }
                                Err(_) => {
                                    entry.insert(None);  
                                    None
                                }
                            }
                        } else {
                            entry.insert(None); 
                            None
                        }
                    }
                }
            }
            Err(_) => None,
        }
    }
}

struct ModuleFilesAudit {
    warn_on_extra: bool,
}

impl ModuleFilesAudit {
    fn new(warn_on_extra: bool) -> Self {
        Self { warn_on_extra }
    }
}

impl Audit for ModuleFilesAudit {
    fn name(&self) -> &str { "Module Files and Impl Check" }

    fn as_any(&self) -> &dyn std::any::Any { self }

    fn run(&self, context: &AuditContext) -> Vec<AuditFinding> {
        let mut findings = Vec::new();

        for (app_name, app) in &context.workspace.app_map {
            if !app.in_build { continue; }

            let module_dir = app.abs_path.join("src/module");
            if !module_dir.exists() {
                let rel_path = to_relative_path(&module_dir, &context.workspace.path);
                findings.push(AuditFinding::new(AuditOutcome::Fail, format!("Missing module directory for app: {app_name}"), Some(rel_path)));
                continue;
            }

            // Collect actual .rs files (skipping mod.rs)
            let mut actual_files = Vec::new();
            if let Ok(entries) = std::fs::read_dir(&module_dir) {
                for entry in entries.flatten() {
                        let path = entry.path();
                        if path.extension().is_some_and(|ext| ext == "rs") {
                            let file_stem = path.file_stem().unwrap_or_default().to_string_lossy().to_string();
                            if file_stem != "mod" {
                                actual_files.push(file_stem);
                            }
                        }
                }
            } else {
                let rel_path = to_relative_path(&module_dir, &context.workspace.path);
                findings.push(AuditFinding::new(AuditOutcome::Fail, format!("Failed to read module dir for app: {app_name}"), Some(rel_path)));
                continue;
            }

            // Compare to extends_expanded 
            let expected_files: Vec<String> = app.extends_expanded.iter().map(|crate_name| crate_name.0.to_lowercase().replace('-', "_")).collect(); // Adjust mapping as needed

            // Missing files
            for expected in &expected_files {
                if !actual_files.contains(expected) {
                    let file_path = module_dir.join(format!("{expected}.rs"));
                    let rel_path = to_relative_path(&file_path, &context.workspace.path);
                    findings.push(AuditFinding::new(AuditOutcome::Fail, format!("Missing module file for extended crate: {expected} in app {app_name}"), Some(rel_path)));
                } else {
                    findings.push(AuditFinding::new(AuditOutcome::Pass, format!("Module file found for: {expected} in app {app_name}"), None));
                }
            }

            // Extra files (if configured)
            if self.warn_on_extra {
                for actual in &actual_files {
                    if !expected_files.contains(actual) {
                        let file_path = module_dir.join(format!("{actual}.rs"));
                        let rel_path = to_relative_path(&file_path, &context.workspace.path);
                        findings.push(AuditFinding::new(AuditOutcome::Warn, format!("Extra module file: {actual} in app {app_name}"), Some(rel_path)));
                    }
                }
            }

            // Check contents/impls for each file that corresponds to an extended crate
            for expected_file in &expected_files {
                if actual_files.contains(expected_file) {
                    let path = module_dir.join(format!("{expected_file}.rs"));
                    if let Some(ast) = context.get_ast(&path) {
                        // Convert file_stem back to crate name (reverse the transformation)
                        let _extended_crate_name = expected_file.replace('_', "-");

                        // Check for specific impls in AST
                        let expected_impls = vec![
                            format!("impl{}::Appforcrate::AppStruct", expected_file),
                            format!("impl{}::Identityforcrate::IdentityStruct", expected_file),
                        ];

                        let missing_impls = check_ast_for_missing_impls(&ast, &expected_impls);

                        if missing_impls.is_empty() {
                            let rel_path = to_relative_path(&path, &context.workspace.path);
                            findings.push(AuditFinding::new(AuditOutcome::Pass, format!("Required impls found in: {expected_file} for app {app_name}"), Some(rel_path)));
                        } else {
                            for missing_impl in missing_impls {
                                let rel_path = to_relative_path(&path, &context.workspace.path);
                                findings.push(AuditFinding::new(AuditOutcome::Fail, format!("Missing impl '{missing_impl}' in: {expected_file} for app {app_name}"), Some(rel_path)));
                            }
                        }
                    } else {
                        let rel_path = to_relative_path(&path, &context.workspace.path);
                        findings.push(AuditFinding::new(AuditOutcome::Fail, format!("Failed to parse: {expected_file} for app {app_name}"), Some(rel_path)));
                    }
                }
            }
        }

        findings
    }
}

// Helper function to convert an absolute path to a relative path
fn to_relative_path(abs_path: &Path, workspace_path: &Path) -> PathBuf {
    abs_path.strip_prefix(workspace_path)
        .unwrap_or(abs_path)
        .to_path_buf()
}

// Helper function to check for missing implementations
fn check_ast_for_missing_impls(ast: &syn::File, expected_impls: &[String]) -> Vec<String> {
    let mut found_impls = Vec::new();

    // Manually traverse the AST to find impl blocks
    for item in &ast.items {
        if let syn::Item::Impl(impl_item) = item {
            // Check if this is a trait implementation 
            if let Some((_, trait_path, _)) = &impl_item.trait_ {
                // Convert trait path to string
                let trait_name = path_to_string(trait_path);

                // Convert self type to string
                let self_type = type_to_string(&impl_item.self_ty);

                // Create the impl string
                let impl_string = format!("impl {trait_name} for {self_type}");
                found_impls.push(impl_string);
            }
        }
    }

    // Check which expected impls are missing
    let mut missing_impls = Vec::new();
    for expected in expected_impls {
        let expected_normalized = normalize_impl_string(expected);
        let found = found_impls.iter().any(|found_impl| {
            let found_normalized = normalize_impl_string(found_impl);
            // Check if the normalized strings match
            found_normalized == expected_normalized
        });

        if !found {
            missing_impls.push(expected.clone());
        }
    }

    missing_impls
}

// Helper function to convert a syn::Path to a string
fn path_to_string(path: &syn::Path) -> String {
    path.segments
        .iter()
        .map(|segment| segment.ident.to_string())
        .collect::<Vec<_>>()
        .join("::")
}

// Helper function to convert a syn::Type to a string
fn type_to_string(ty: &syn::Type) -> String {
    match ty {
        syn::Type::Path(type_path) => {
            if let Some(qself) = &type_path.qself {
                // Handle qualified self types like <T as Trait>::Type
                format!("<{} as {}>::{}",
                    type_to_string(&qself.ty),
                    path_to_string(&type_path.path),
                    type_path.path.segments.last().unwrap().ident
                )
            } else {
                path_to_string(&type_path.path)
            }
        },
        syn::Type::Reference(type_ref) => {
            format!("&{}{}",
                if type_ref.mutability.is_some() { "mut " } else { "" },
                type_to_string(&type_ref.elem)
            )
        },
        syn::Type::Tuple(type_tuple) => {
            let elements: Vec<String> = type_tuple.elems
                .iter()
                .map(type_to_string)
                .collect();
            format!("({})", elements.join(", "))
        },
        _ => {
            // For other types, return a generic placeholder
            // This is a fallback for complex types we don't handle
            "UnknownType".to_string()
        }
    }
}

// Helper function to normalize impl strings for comparison
fn normalize_impl_string(impl_str: &str) -> String {
    impl_str
        .replace(" ", "")
        .replace("\t", "")
        .replace("\n", "")
        .replace("{", "")
        .replace("}", "")
        .replace(";", "")
        .to_lowercase()
}

struct DependencyVersionAudit;

impl DependencyVersionAudit {
    fn new() -> Self {
        Self
    }
}

impl Audit for DependencyVersionAudit {
    fn name(&self) -> &str {
        "dependency_version"
    }

    fn as_any(&self) -> &dyn std::any::Any { self }

    fn run(&self, context: &AuditContext) -> Vec<AuditFinding> {
        let mut findings = Vec::new();

        for crate_ref in context.workspace.crate_map.values() {
            for (dep_name, dep_struct) in &crate_ref.dependencies {
                if dep_struct.version.is_some() {
                    let rel_path = to_relative_path(&crate_ref.cargo_toml_path, &context.workspace.path);
                    findings.push(AuditFinding {
                        audit_outcome: AuditOutcome::Warn,
                        message: format!("Dependency `{dep_name}` has a version specified.  It should be in the workspace, or have path specified."),
                        file: Some(rel_path),
                        line: None,
                        suggestion: Some(format!("Remove the version from the dependency, or add `path = \"../{dep_name}\"` if it should be in the workspace.")),

                    });
                }
            }
        }

        findings
    }           
}

struct DependencyExtendsMatchAudit {
    // Store analysis results to avoid double parsing
    analysis_results: std::sync::Mutex<Vec<DependencyAnalysisResult>>,
}

#[derive(Clone)]
struct DependencyAnalysisResult {
    crate_ref: String, // Store crate name instead of reference to avoid lifetime issues
    _workspace_deps: std::collections::HashSet<String>,
    extra_deps: Vec<String>,
    missing_deps: Vec<String>,
}

impl DependencyExtendsMatchAudit {
    fn new() -> Self {
        Self {
            analysis_results: std::sync::Mutex::new(Vec::new())
        }
    }

    /// Filter dependencies to only include workspace-internal dependencies
    fn get_workspace_dependencies(&self, crate_ref: &crate::Crate, workspace: &crate::Workspace) -> std::collections::HashSet<String> {
        crate_ref.dependencies_names_set
            .iter()
            .filter(|dep_name| {
                // Only include dependencies that are also workspace crates
                workspace.crate_map.contains_key(&crate::CrateName((*dep_name).clone()))
            })
            .cloned()
            .collect()
    }



    /// Print rich output for all application crates using stored analysis results
    fn print_rich_output_for_stored_results(&self) {
        if let Ok(stored_results) = self.analysis_results.lock() {
            for result in stored_results.iter() {
                self.print_rich_output_from_analysis(result);
            }
        }
    }

    /// Print rich output from stored analysis result
    fn print_rich_output_from_analysis(&self, result: &DependencyAnalysisResult) {
        println!("\n=== Cargo.toml Dependency Analysis for {} ===", result.crate_ref);

        let synchronized = result.extra_deps.is_empty() && result.missing_deps.is_empty();

        println!("\n📊 Summary:");
        println!(
            "   Synchronized: {}",
            if synchronized { "YES ✅" } else { "NO ❌" }
        );
        println!("  - Extra deps: {}", result.extra_deps.len());
        println!("  - Missing deps: {}", result.missing_deps.len());
        println!();

        if synchronized {
            println!("✅ Dependencies are synchronized!");
        } else {
            println!("❌ Dependencies are NOT synchronized!");
        }

        if !result.extra_deps.is_empty() {
            println!("\n🔴 Extra dependencies (not in extends):");
            for dep in &result.extra_deps {
                println!("  - {dep}");
            }
        }

        if !result.missing_deps.is_empty() {
            println!("\n🟡 Missing dependencies (in extends but not in Cargo.toml):");
            for dep in &result.missing_deps {
                println!("  - {dep}");
            }
        }
    }
}

impl Audit for DependencyExtendsMatchAudit {
    fn name(&self) -> &str { "Dependencies vs Extends Match" }

    fn as_any(&self) -> &dyn std::any::Any { self }

    fn run(&self, context: &AuditContext) -> Vec<AuditFinding> {
        let mut findings = Vec::new();
        let mut analysis_results = Vec::new();

        // Filter to only application crates (not library/module crates)
        for crate_ref in context.workspace.get_build_crates().into_iter().filter(|c| c.app_config.is_some()) {
            // Filter to only workspace-internal dependencies
            let workspace_deps = self.get_workspace_dependencies(crate_ref, context.workspace);

            let extra_deps: Vec<String> = workspace_deps
                .difference(&crate_ref.extends_names_set)
                .cloned()
                .collect();

            let missing_deps: Vec<String> = crate_ref.extends_names_set
                .difference(&workspace_deps)
                .cloned()
                .collect();

            let extra_deps_empty = extra_deps.is_empty();
            let missing_deps_empty = missing_deps.is_empty();

            // Store analysis results for later rich output printing
            analysis_results.push(DependencyAnalysisResult {
                crate_ref: crate_ref.crate_name.0.clone(),
                _workspace_deps: workspace_deps.clone(),
                extra_deps: extra_deps.clone(),
                missing_deps: missing_deps.clone(),
            });

            // Generate audit findings
            for dep in &extra_deps {
                let rel_path = to_relative_path(&crate_ref.cargo_toml_path, &context.workspace.path);
                findings.push(AuditFinding::new(
                    AuditOutcome::Warn,
                    format!("Extra workspace dependency not in extends: {dep}"),
                    Some(rel_path)
                ));
            }
            for dep in &missing_deps {
                let rel_path = to_relative_path(&crate_ref.cargo_toml_path, &context.workspace.path);
                findings.push(AuditFinding::new(
                    AuditOutcome::Fail,
                    format!("Missing workspace dependency from extends: {dep}"),
                    Some(rel_path)
                ));
            }
            if extra_deps_empty && missing_deps_empty {
                findings.push(AuditFinding::new(
                    AuditOutcome::Pass,
                    format!("Dependencies match extends for crate {}", crate_ref.crate_name),
                    None
                ));
            }
        }

        // Store analysis results for rich output
        if let Ok(mut stored_results) = self.analysis_results.lock() {
            *stored_results = analysis_results;
        }

        findings
    }
}

struct FileExistenceAudit {
    expected_files: Vec<String>, // Relative to crate abs_path
}

impl FileExistenceAudit {
    fn new(expected_files: Vec<String>) -> Self { Self { expected_files } }
}

impl Audit for FileExistenceAudit {
    fn name(&self) -> &str { "File Existence Check" }

    fn as_any(&self) -> &dyn std::any::Any { self }

    fn run(&self, context: &AuditContext) -> Vec<AuditFinding> {
        let mut findings = Vec::new();
        for crate_ref in context.workspace.crate_map.values() {
            for rel_file in &self.expected_files {
                let full_path = crate_ref.abs_path.join(rel_file);
                if full_path.exists() {
                    let rel_path = to_relative_path(&full_path, &context.workspace.path);
                    findings.push(AuditFinding::new(AuditOutcome::Pass, format!("File exists: {rel_file}"), Some(rel_path)));
                } else {
                    let rel_path = to_relative_path(&crate_ref.abs_path, &context.workspace.path);
                    findings.push(AuditFinding::new(AuditOutcome::Fail, format!("Missing file: {rel_file}"), Some(rel_path)));
                }
            }
        }
        findings
    }
}

struct ItemPresenceAudit {
    rel_file: String, // "src/lib.rs"
    expected_items: Vec<String>, // ["pub struct Foo", "fn bar()"]
    warn_on_extra: bool,
}

impl ItemPresenceAudit {
    fn new(rel_file: String, expected_items: Vec<String>, warn_on_extra: bool) -> Self {
        Self { rel_file, expected_items, warn_on_extra }
    }
}

impl Audit for ItemPresenceAudit {
    fn name(&self) -> &str { "Item Presence Check" }

    fn as_any(&self) -> &dyn std::any::Any { self }

    fn run(&self, context: &AuditContext) -> Vec<AuditFinding> {
        let mut findings = Vec::new();
        for app in context.workspace.get_all_apps() { // Or get_build_apps()
            let file_path = app.abs_path.join(&self.rel_file);
            if let Some(ast) = context.get_ast(&file_path) {
                let found_items = extract_items_from_ast(&ast); // Hypothetical fn to get item signatures as strings
                for expected in &self.expected_items {
                    if !found_items.contains(expected) {
                        let rel_path = to_relative_path(&file_path, &context.workspace.path);
                        findings.push(AuditFinding::new(AuditOutcome::Fail, format!("Missing item: {expected}"), Some(rel_path)));
                    } else {
                        let rel_path = to_relative_path(&file_path, &context.workspace.path);
                        findings.push(AuditFinding::new(AuditOutcome::Pass, format!("Item found: {expected}"), Some(rel_path)));
                    }
                }
                if self.warn_on_extra {
                    for found in &found_items {
                        if !self.expected_items.contains(found) {
                            let rel_path = to_relative_path(&file_path, &context.workspace.path);
                            findings.push(AuditFinding::new(AuditOutcome::Warn, format!("Extra item: {found}"), Some(rel_path)));
                        }
                    }
                }
            } else {
                let rel_path = to_relative_path(&file_path, &context.workspace.path);
                findings.push(AuditFinding::new(AuditOutcome::Fail, format!("Failed to parse file: {}", self.rel_file), Some(rel_path)));
            }
        }
        findings
    }
}

// Helper (implement based on syn visitors)
fn extract_items_from_ast(ast: &SynFile) -> Vec<String> {
    // Use syn::visit to collect item names/signatures
    // Stub: collect item names as strings
    ast.items.iter().enumerate().map(|(i, _item)| format!("item_{i}")).collect()
}

#[derive(Deserialize)]
struct AuditConfigEntry {
    #[serde(rename = "type")]
    type_: String,
    expected_files: Option<Vec<String>>,
    file: Option<String>,
    expected_items: Option<Vec<String>>,
    warn_on_extra: Option<bool>,
}

#[derive(Deserialize, Default)]
struct AuditConfig {
    audits: Vec<AuditConfigEntry>,
}

fn load_audits(context: &AuditContext) -> Vec<Box<dyn Audit>> {
    let config_path = context.workspace.path.join("audit.toml");
    if config_path.exists() {
        let config_content = std::fs::read_to_string(&config_path).ok();
        if let Some(content) = config_content {
            match toml::from_str::<AuditConfig>(&content) {
                Ok(config) => {
                    return config.audits.into_iter().map(|entry| -> Box<dyn Audit> {
                        match entry.type_.as_str() { // Add more as needed
                        "dependency_version" => Box::new(DependencyVersionAudit::new()),
                        "dependency_extends_match" => Box::new(DependencyExtendsMatchAudit::new()),
                        "file_existence" => Box::new(FileExistenceAudit::new(entry.expected_files.unwrap_or_default())),
                        "item_presence" => Box::new(ItemPresenceAudit::new(entry.file.unwrap(), entry.expected_items.unwrap_or_default(), entry.warn_on_extra.unwrap_or(false))),
                        "module_files" => Box::new(ModuleFilesAudit::new(entry.warn_on_extra.unwrap_or(false))),
                        _ => Box::new(InvalidAudit::new(entry.type_)),
                        }
                    }).collect();
                }
                Err(_e) => {
                    // Fall back to default audits if config parsing fails
                }
            }
        }
    }
    
    vec![
        Box::new(DependencyVersionAudit::new()), 
        Box::new(DependencyExtendsMatchAudit::new()),
        Box::new(FileExistenceAudit::new(vec!["src/lib.rs".to_string(), "Cargo.toml".to_string()])),
        Box::new(ItemPresenceAudit::new("src/lib.rs".to_string(), vec!["pub struct Foo".to_string()], false)),
        Box::new(ModuleFilesAudit::new(false)),
        // Add more as needed
    ]
}

// Placeholder for invalid
struct InvalidAudit { type_: String }

impl InvalidAudit {
    fn new(type_: String) -> Self {
        Self { type_ }
    }
}

impl Audit for InvalidAudit {
    fn name(&self) -> &str { "Invalid Audit" }

    fn as_any(&self) -> &dyn std::any::Any { self }

    fn run(&self, _context: &AuditContext) -> Vec<AuditFinding> {
        vec![AuditFinding::new(AuditOutcome::Fail, format!("Unknown audit type: {}", self.type_), None)]
    }
}

pub(super) fn audit(workspace: &'static crate::Workspace) {
    let verbose_timing = std::env::var("ACP_AUDIT_TIMING").is_ok();
    let total_start = std::time::Instant::now();
    let initial_memory = get_memory_usage();

    let context_start = std::time::Instant::now();
    let context = AuditContext::new(workspace);
    let context_duration = context_start.elapsed();

    let load_start = std::time::Instant::now();
    let audits = load_audits(&context);
    let load_duration = load_start.elapsed();

    let mut reports = Vec::new();
    let mut audit_timings = Vec::new();

    for audit in &audits {
        let audit_start = std::time::Instant::now();
        let findings = audit.run(&context);
        let audit_duration = audit_start.elapsed();

        audit_timings.push((audit.name().to_string(), audit_duration));

        let summary = AuditSummary::from(&findings);
        reports.push(AuditReport {
            audit_name: audit.name().to_string(),
            findings,
            summary,
        });
    }

    // Reporting
    let mut stdout = termcolor::StandardStream::stdout(termcolor::ColorChoice::Auto);

    for report in &reports {
        // Summary
        stdout.set_color(termcolor::ColorSpec::new().set_fg(Some(termcolor::Color::Cyan))).ok();
        writeln!(stdout, "Audit: {}", report.audit_name).ok();
        writeln!(stdout, "Summary: {} passes, {} warns, {} fails", report.summary.passes, report.summary.warnings, report.summary.failures).ok();
        stdout.reset().ok();

        for finding in &report.findings {
            // Skip passes unless verbose (add flag if needed)
            if matches!(finding.audit_outcome, AuditOutcome::Pass) { continue; }

            let color = match finding.audit_outcome {
                AuditOutcome::Warn => termcolor::Color::Yellow,
                AuditOutcome::Fail => termcolor::Color::Red,
                _ => termcolor::Color::Green,
            };
            stdout.set_color(termcolor::ColorSpec::new().set_fg(Some(color))).ok();
            let file_str = finding.file.as_ref().map(|p| p.display().to_string()).unwrap_or_default();
            writeln!(stdout, "[{:?}] {} at {}:{}", finding.audit_outcome, finding.message, file_str, finding.line.unwrap_or(0)).ok();
            if let Some(suggestion) = &finding.suggestion {
                writeln!(stdout, "Suggestion: {suggestion}").ok();
            }
            stdout.reset().ok();
        }

        // Print rich output for Dependencies vs Extends Match audit
        if report.audit_name == "Dependencies vs Extends Match" {
            // Find the audit instance that has the stored results
            for audit in &audits {
                if audit.name() == "Dependencies vs Extends Match" {
                    if let Some(dep_audit) = audit.as_any().downcast_ref::<DependencyExtendsMatchAudit>() {
                        dep_audit.print_rich_output_for_stored_results();
                    }
                    break;
                }
            }
        }
    }

    let total_duration = total_start.elapsed();
    let final_memory = get_memory_usage();

    // Print timing information (always show basic timing, verbose with env var)
    if verbose_timing {
        println!("\n🕐 Audit Performance Summary:");
        println!("  Context creation: {context_duration:?}");
        println!("  Config loading: {load_duration:?}");
        println!("  Individual audit timings:");
        for (audit_name, duration) in &audit_timings {
            println!("    {audit_name}: {duration:?}");
        }
        println!("  Total audit time: {total_duration:?}");

        // Print memory information if available
        match (initial_memory, final_memory) {
            (Some(initial), Some(final_mem)) => {
                println!("  Memory usage: {} KB -> {} KB (delta: {} KB)",
                    initial, final_mem, final_mem as i64 - initial as i64);
            }
            _ => println!("  Memory usage: Not available on this platform"),
        }
    } else {
        println!("\n⏱️  Audit completed in {total_duration:?} (set ACP_AUDIT_TIMING=1 for detailed timing)");
    }

    // Run existing cargo check
    let cargo_start = std::time::Instant::now();
    crate::process::call_cargo_all(workspace, &["check"]);
    let cargo_duration = cargo_start.elapsed();

    if verbose_timing {
        println!("  Cargo check time: {cargo_duration:?}");
        println!("  Grand total: {:?}", total_start.elapsed());
    }


    if reports.iter().any(|r| r.summary.failures > 0) {
        std::process::exit(1);
    }
}
