use clap::{Parser, Subcommand};

mod audit;
mod aws_sdk;
mod build;
mod check;
mod clean;
mod format;
mod init;
mod route;
mod run;
mod tests;
mod util;

use std::path::PathBuf;

/// A fictional versioning CLI
#[derive(Debug, Parser)] // requires `derive` feature
#[command(name = "acp")]
#[command(about = "AppCove Platform", long_about = None)]
struct Cli {
    #[clap(
        short = 'C',
        long,
        help = "Run in this directory instead of the current directory"
    )]
    current_directory: Option<PathBuf>,

    #[clap(
        long = "require-version",
        help = "require this version to be the one running or die with an error"
    )]
    require_version: Option<String>,

    #[command(subcommand)]
    command: Commands,
}

#[derive(Debug, Subcommand)]
enum Commands {
    // Init
    #[clap(about = "Initialize or Re-Initialize the workspace")]
    Init,

    #[clap(about = "Build configured projects")]
    Build {
        #[clap(short, long, help = "Build only these packages")]
        packages: Vec<String>,

        #[clap(long, help = "Release build")]
        release: bool,

        #[clap(long, help = "Prepare the build without actually running cargo build")]
        prep_only: bool,
    },

    #[clap(about = "Build and run configured projects")]
    Run {
        #[clap(short, long, help = "Build only these packages")]
        packages: Vec<String>,

        #[clap(long, help = "Release build")]
        release: bool,
    },

    #[clap(about = "Run configured projects, assuming they are already built")]
    RunOnly {
        #[clap(short, long, help = "Build only these packages")]
        packages: Vec<String>,

        #[clap(long, help = "Release build")]
        release: bool,
    },

    #[clap(about = "Lint and check the codebase")]
    Check,

    #[clap(about = "Format the codebase")]
    Format {
        #[clap(long, help = "Format all files, not just modified files")]
        all: bool,
    },

    #[clap(about = "Run unit tests for configured projects")]
    Test {
        #[clap(short, long)]
        package: Vec<String>,

        #[clap(subcommand)]
        subcommand: Option<TestCommand>,
    },

    #[clap(about = "Routing information for this workspace")]
    Route {
        // multiple -p should be accepted
        #[clap(short, long)]
        package: Vec<String>,

        // Debug flag
        #[clap(short, long)]
        debug: bool,

        // Verbose flag
        #[clap(short, long)]
        verbose: bool,

        #[clap(long, conflicts_with = "no-color")]
        color: bool,

        // Color flag
        #[arg(long, conflicts_with = "color")]
        no_color: bool,

        // show map files
        #[clap(long)]
        show_source_maps: bool,

        // show nodes instead of routes
        #[clap(long, help = "Show nodes instead of routes")]
        raw: bool,

        // Optional string should be used as an argument for the filter
        #[clap()]
        filters: Option<Vec<String>>,
    },

    #[clap(about = "View info on this workspace")]
    Workspace,

    #[clap(about = "Audit the workspace for potential issues")]
    Audit,

    #[clap(about = "Cleans up all build artifacts")]
    Clean,

    #[clap(about = "Manage the aws-sdk custom builds")]
    AwsSdk {
        #[clap(subcommand)]
        subcommand: AwsSdkCommands,
    },

    #[clap(about = "Utility commands")]
    Util {
        #[clap(subcommand)]
        subcommand: UtilityCommands,
    },

    #[clap(about = "Print Version")]
    Version,
}

#[derive(Debug, Subcommand)]
enum UtilityCommands {
    #[clap(about = "Output a randomly generated 256 bit key in base64")]
    GenerateAesKey,
}

#[derive(Debug, Parser)]
struct AllTheArgs {
    args: Vec<String>,
}

#[derive(Debug, Subcommand)]
enum AwsSdkCommands {
    #[clap(about = "Clone smithy-rs and set on proper commit")]
    Clone,

    #[clap(about = "Set smithy-rs repo to proper state, modify input files, and build the aws-sdk")]
    Build,

    #[clap(about = "Push the generated aws-sdk to the repo")]
    Push,

    #[clap(about = "Clean-up temp files")]
    Clean,
}

#[derive(Debug, Subcommand)]
enum TestCommand {
    #[clap(about = "Start Postgres Docker Container, and run tests")]
    Postgres,

    #[clap(about = "Start Redis Docker Container, and run tests")]
    Redis,

    #[clap(about = "Run rust tests")]
    Rust,

    #[clap(about = "Run deno tests")]
    Deno,
}

pub async fn main() {
    let args = Cli::parse();

    // perform version check, but only if Version wasn't the requested command
    if !matches!(args.command, Commands::Version) {
        if let Some(require_version) = args.require_version {
            let current_version = env!("CARGO_PKG_VERSION");
            if current_version != require_version {
                eprintln!(
                    "Error: `acp` version mismatch. Require version `{require_version}`, but this is version `{current_version}`.\n\nRun `./acp init` to update."
                );
                std::process::exit(1);
            }
        }
    }

    // get the current working directory
    let cwd = match args.current_directory {
        Some(ref cwd) => cwd.to_owned(),
        None => std::env::current_dir().expect("could not get current directory"),
    };

    let workspace: &'static crate::Workspace = Box::leak(Box::new(crate::Workspace::init(&cwd)));

    match args.command {
        Commands::Init => {
            self::init::init(workspace);
        }

        Commands::Build {
            packages,
            release,
            prep_only,
        } => {
            let packages = if packages.is_empty() {
                None
            } else {
                Some(packages.as_slice())
            };
            self::build::build(workspace, packages, release, prep_only);
        }

        Commands::Run { packages, release } => {
            let packages = if packages.is_empty() {
                None
            } else {
                Some(packages.as_slice())
            };
            self::run::run(workspace, packages, release);
        }

        Commands::RunOnly { packages, release } => {
            let packages = if packages.is_empty() {
                None
            } else {
                Some(packages.as_slice())
            };
            self::run::run_only(workspace, packages, release);
        }

        Commands::Check => {
            self::check::check(workspace);
        }

        Commands::Format { all } => {
            self::format::fmt(workspace, all);
        }

        Commands::Test {
            package,
            subcommand,
        } => match subcommand {
            Some(TestCommand::Postgres) => self::tests::test_postgres(workspace, package),
            Some(TestCommand::Redis) => self::tests::test_redis(workspace, package),
            Some(TestCommand::Rust) => self::tests::test_rust(workspace, package),
            Some(TestCommand::Deno) => self::tests::test_deno(workspace, package),
            None => self::tests::test(workspace, package),
        },

        Commands::Route {
            package,
            debug,
            verbose,
            color,
            no_color,
            show_source_maps,
            raw,
            filters,
        } => {
            let package = if package.is_empty() {
                None
            } else {
                Some(package.as_slice())
            };
            let color = match (color, no_color) {
                (false, false) => true, // default is true
                (true, false) => true,  // --color
                (false, true) => false, // --no-color
                _ => panic!("Cannot specify both --color and --no-color"),
            };
            self::route::route(
                workspace,
                package,
                debug,
                verbose,
                color,
                show_source_maps,
                raw,
                filters,
            )
            .expect("route failed");
        }

        Commands::Workspace => {
            println!("Workspace: {workspace:#?}");

            for app in workspace.get_all_apps() {
                println!("App: {app:#?}");
            }

            for app in workspace.get_build_apps() {
                println!("Build App: {app:#?}");
                for crate_ref in app.extends_expanded.iter() {
                    println!("Extended Crate: {crate_ref:#?}");
                }
            }

            for crate_ref in workspace.get_build_crates() {
                println!("Crate: {crate_ref:#?}");
            }
        }

        Commands::Audit => {
            self::audit::audit(workspace);
        }

        Commands::Clean => {
            self::clean::clean(workspace);
        }

        Commands::AwsSdk { subcommand } => match subcommand {
            AwsSdkCommands::Clone => {
                self::aws_sdk::clone(workspace);
            }
            AwsSdkCommands::Build => {
                self::aws_sdk::build(workspace).await;
            }
            AwsSdkCommands::Clean => {
                self::aws_sdk::clean(workspace);
            }
            AwsSdkCommands::Push => {
                self::aws_sdk::push(workspace);
            }
        },

        Commands::Util { subcommand } => match subcommand {
            UtilityCommands::GenerateAesKey => {
                self::util::generate_aes_key();
            }
        },

        Commands::Version => {
            println!("acp version: {}", env!("CARGO_PKG_VERSION"));
        }
    }
}
