mod aws_sdk;
mod build;
pub mod cli;
mod parser;
mod process;
mod schema;
mod util;

use indexmap::IndexMap;
use itertools::Itertools;
use serde::Deserialize;

use std::collections::{HashMap, HashSet};
use std::ops::Deref;
use std::path::{Path, PathBuf};

#[derive(Clone, PartialEq, Eq, Hash, PartialOrd, Ord)]
pub struct CrateName(String);
impl Deref for CrateName {
    type Target = str;
    fn deref(&self) -> &Self::Target {
        &self.0
    }
}

#[derive(Clone, PartialEq, Eq, Hash, PartialOrd, Ord)]
pub struct DocumentIdent(String);
impl Deref for DocumentIdent {
    type Target = str;
    fn deref(&self) -> &Self::Target {
        &self.0
    }
}
impl std::fmt::Debug for DocumentIdent {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.0)
    }
}
impl std::fmt::Display for DocumentIdent {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.0)
    }
}
impl DocumentIdent {
    pub fn new(name: &str) -> Result<Self, String> {
        if !name.starts_with("Document") {
            return Err(format!(
                "DocumentName must start with `Document`, got `{name}`"
            ));
        }
        if !name.chars().all(|c| c.is_alphanumeric() || c == '_') {
            return Err(format!(
                "DocumentName must be a valid rust identifier, got `{name}`"
            ));
        }
        Ok(Self(name.to_owned()))
    }
}

#[derive(Debug)]
pub struct Workspace {
    pub path: PathBuf,
    pub workspace_cargo_toml_path: PathBuf,
    pub local_toml_path: PathBuf,
    pub crate_map: IndexMap<CrateName, Crate>,
    pub app_map: IndexMap<CrateName, Application>,
    pub acp_config: acp_config::AcpConfig,
}

// create a struct to represent a member project
#[derive(Debug)]
pub struct Crate {
    pub ident: String,
    pub crate_name: CrateName,
    pub rel_path: String,
    pub cargo_toml_path: PathBuf,
    pub abs_path: PathBuf,
    pub version: String,
    pub edition: String,
    pub dependencies: IndexMap<String, CrateDependency>,

    /// This is the actual items from the Cargo.toml, not a sorted MRO
    pub extends: Vec<CrateName>,

    
    pub dependencies_names_set: std::collections::HashSet<String>,
    
    pub extends_names_set: std::collections::HashSet<String>,

    pub app_config: Option<CrateApp>,

    pub mod_config: Option<CrateMod>,

    /// this crate is included in the current build
    pub in_build: bool,

    /// Crate config will be added here
    pub config: toml::Table,
}

#[derive(Debug)]
pub struct CrateDependency {
    pub workspace: bool,
    pub version: Option<String>,
    pub path: Option<String>,
}

#[derive(Debug)]
pub struct CrateApp {
    pub port: u16,
    pub crate_to_document_to_ident: IndexMap<CrateName, IndexMap<DocumentIdent, DocumentIdent>>,
}

#[derive(Debug)]
pub struct CrateMod {}

/// This is the struct that represents a crate which is a top level application, referenced in LOCAL.yaml
/// It contains a vec of topologically sorted crate names, where the first crate is same as the Application itself, and the last is the furthest dependancy
#[derive(Debug)]
pub struct Application {
    pub ident: String,
    pub crate_name: CrateName,
    pub rel_path: String,
    pub abs_path: PathBuf,

    /// this app is included in the current build
    pub in_build: bool,

    /// App config will be added here
    pub config: toml::Table,

    /// This is a vec of topologically sorted crate names,
    /// where the first crate is same as AppCrate itself, and the last is the furthest dependancy
    pub extends_expanded: Vec<CrateName>,

    /// Doc map is a nested mapping:
    /// {
    ///     (CrateName, DocumentIdent) => DocumentIdent,
    ///     ...
    /// }
    pub doc_map: IndexMap<(CrateName, DocumentIdent), DocumentIdent>,
}




impl Workspace {
    /// This is how you create a Workspace instance, and should only be called once typically, notwithstanding tests.
    pub fn init(orig_cwd: &Path) -> Self {
        // assert that orig_cwd is an absolute path
        assert!(orig_cwd.is_absolute());

        let mut port_to_name_map = HashMap::new();

        // find the root of the git repo
        let path = {
            let mut cwd = orig_cwd.to_owned();
            loop {
                if cwd.join(".git").exists() {
                    break cwd;
                }
                if !cwd.pop() {
                    panic!("Not in a git repo at {}", orig_cwd.display());
                };
            }
        };

        let workspace_cargo_toml_path = path.join("Cargo.toml");
        let local_toml_path = path.join("LOCAL.toml");

        // open and read LOCAL.toml
        let local_toml: crate::parser::local_toml::LocalToml = toml::from_str(
            &std::fs::read_to_string(&local_toml_path).expect("Error reading LOCAL.toml"),
        )
        .expect("Error parsing LOCAL.toml");

        // open and read Cargo.toml
        let workspace_cargo_toml =
            std::fs::read_to_string(&workspace_cargo_toml_path).expect("Error reading Cargo.toml");

        // parse top level Cargo.toml
        let workspace_cargo_toml: crate::parser::workspace_cargo_toml::WorkspaceCargoToml =
            toml::from_str(&workspace_cargo_toml).expect("Error parsing Cargo.toml");

        // create a map of workspace crates - every crate referenced by the top level Cargo.toml
        let mut crate_map: IndexMap<CrateName, Crate> =
            IndexMap::with_capacity(workspace_cargo_toml.workspace.members.len());

        for member in &workspace_cargo_toml.workspace.members {
            let crate_ref = Crate::load(&path, member);

            // validate for unique port numbers
            if let Some(crate_app) = &crate_ref.app_config {
                if port_to_name_map.contains_key(&crate_app.port) {
                    panic!(
                        "Error: port {} is in use by both `{}` and `{}`.  Look in the respective Cargo.toml's to sort it out.",
                        crate_app.port, crate_ref.crate_name.0, port_to_name_map[&crate_app.port]
                    );
                }
                port_to_name_map.insert(crate_app.port, crate_ref.crate_name.0.to_owned());
            }

            crate_map.insert(crate_ref.crate_name.clone(), crate_ref);
        }

        // create a map of app crates... this is any that have `is_app = true` in their `Cargo.toml`
        let mut app_map: IndexMap<CrateName, Application> = crate_map
            .iter()
            .filter_map(|(crate_name, crate_ref)| {
                crate_ref.app_config.as_ref().map(|crate_app| {
                    // create the default config (presently, only port is included)
                    let mut webserver = toml::Table::new();
                    webserver.insert(
                        "port".to_string(),
                        toml::Value::Integer(crate_app.port as i64),
                    );

                    let mut config = toml::Table::new();
                    config.insert("webserver".to_string(), toml::Value::Table(webserver));

                    let extends_expanded = Application::extends_expand(crate_ref, &crate_map);

                    // validate that all the crates in docmap are in extends_expanded
                    let doc_map = match &crate_ref.app_config {
                        Some(crate_app) => {
                            let mut doc_map = IndexMap::new();
                            // iterate over each 
                            //  key (CrateName)
                            //  value (map of DocumentName => DocumentPath)
                            for (crate_name, doc_name_to_doc_ident_map) in &crate_app.crate_to_document_to_ident {
                                let crate_name = crate_name.clone();

                                // validate the crate name is in the extends_expanded list
                                if !extends_expanded.contains(&crate_name) {
                                    panic!(
                                        "Error in `{}`:\n - Attempting to use `app.docmap.{}`...\n - `{}` is not the list of extended crates:\n - [{}]",
                                        crate_ref.cargo_toml_path.display(), crate_name, crate_name, extends_expanded.iter().join(", ")
                                    );
                                }

                                for (document_name, document_ident) in doc_name_to_doc_ident_map {
                                    doc_map.insert((crate_name.clone(), document_name.clone()), document_ident.clone());
                                }
                        }
                            doc_map
                        }
                        None => IndexMap::new(),
                    };

                    (
                        crate_name.clone(),
                        Application {
                            ident: crate_ref.ident.clone(),
                            crate_name: crate_ref.crate_name.clone(),
                            rel_path: crate_ref.rel_path.clone(),
                            abs_path: crate_ref.abs_path.clone(),
                            extends_expanded,
                            doc_map,

                            // the following may be updated later if this is referenced by LOCAL.toml
                            in_build: false,
                            config,
                        },
                    )
                })
            })
            .collect();

        // update crate_map with any crates referenced in LOCAL.toml
        for (crate_name, crate_toml) in &local_toml.crate_map {
            let crate_name = CrateName(crate_name.clone());
            let crate_ref = match crate_map.get_mut(&crate_name) {
                Some(crate_ref) => crate_ref,
                None => panic!(
                    "`LOCAL.toml` references `[crate.{crate_name}]` which is not defined as a crate in the workspace."
                ),
            };
            crate_ref.in_build = true;
            crate::util::deep_merge_table(
                &mut crate_ref.config,
                crate_toml,
                format!("LOCAL.toml: {crate_name}"),
            );
        }

        // update app_map with any apps referenced in LOCAL.toml
        for (app_name, app_toml) in &local_toml.app_map {
            let app_name = CrateName(app_name.clone());
            let app = match app_map.get_mut(&app_name) {
                Some(app) => app,
                None => match crate_map.get(&app_name) {
                    Some(crate_ref) => panic!(
                        "`LOCAL.toml` references `[app.{0}]` which is defined as a crate, but not an app, in the workspace.  \n - If the intention is to define it as an application, then add `package.metadata.acp.is_app = true` to `{1}/Cargo.toml`.  \n - If the intention is to just add it to the build list, then add `[crate.{0}]` to `LOCAL.toml`",
                        app_name, crate_ref.rel_path
                    ),
                    None => panic!(
                        "`LOCAL.toml` references `[app.{app_name}]` which is not defined as a crate in the workspace."
                    ),
                },
            };
            // extend the

            app.in_build = true;
            crate::util::deep_merge_table(
                &mut app.config,
                app_toml,
                format!("LOCAL.toml: {app_name}"),
            );

            // Iterate over the extends and set those crates to also build
            for crate_name in app.extends_expanded.iter() {
                let crate_ref = match crate_map.get_mut(crate_name) {
                    Some(crate_ref) => crate_ref,
                    None => panic!(
                        "`{app_name}` extends `{crate_name}` which is not defined as a crate in the workspace."
                    ),
                };
                crate_ref.in_build = true;
            }
        }

        let acp_config = acp_config::AcpConfig::read().expect("Error reading acp.json config");

        Workspace {
            path,
            local_toml_path,
            workspace_cargo_toml_path,
            crate_map,
            app_map,
            acp_config,
        }
    }

    pub fn get_build_apps(&self) -> Vec<&Application> {
        self.app_map
            .iter()
            .filter_map(|(_k, v)| if v.in_build { Some(v) } else { None })
            .collect()
    }

    pub fn get_filtered_build_apps(&self, packages: &[String]) -> Vec<&Application> {
        let mut rval = Vec::new();
        for package in packages {
            let crate_name = CrateName(package.clone());
            let application = match self.app_map.get(&crate_name) {
                Some(app) if app.in_build => app,
                Some(app) => panic!(
                    "App `{}` is not configured to build in LOCAL.toml",
                    app.crate_name
                ),
                None => panic!("Could not find app `{package}` in workspace"),
            };
            rval.push(application);
        }
        rval
    }

    pub fn get_all_apps(&self) -> Vec<&Application> {
        self.app_map.values().collect()
    }

    pub fn get_filtered_apps(&self, packages: &[String]) -> Vec<&Application> {
        let mut rval = Vec::new();
        for package in packages {
            let crate_name = CrateName(package.clone());
            let application = match self.app_map.get(&crate_name) {
                Some(app) => app,
                None => panic!("Could not find app `{package}` in workspace"),
            };
            rval.push(application);
        }
        rval
    }

    pub fn get_all_crate_refs_for_some_apps(&self, apps: &[&Application]) -> Vec<&Crate> {
        let mut crates: Vec<&Crate> = apps
            .iter()
            .flat_map(|app| app.extends_expanded.iter())
            .map(|v| {
                self.crate_map
                    .get(v)
                    .unwrap_or_else(|| panic!("Could not load extended crate `{v}`"))
            })
            .collect();
        crates.sort_by(|a, b| a.crate_name.0.cmp(&b.crate_name.0));
        crates.dedup_by(|a, b| a.crate_name.0 == b.crate_name.0);
        crates
    }
    /// Return all crates that are in the build, both modules and not
    pub fn get_build_crates(&self) -> Vec<&Crate> {
        self.crate_map
            .iter()
            .filter_map(|(_k, v)| if v.in_build { Some(v) } else { None })
            .collect()
    }

    pub fn get_filtered_build_crates(&self, packages: &[String]) -> Vec<&Crate> {
        let mut rval = Vec::new();
        for package in packages {
            let crate_name = CrateName(package.clone());
            let crate_ref = match self.crate_map.get(&crate_name) {
                Some(crate_ref) => crate_ref,
                None => panic!("Could not find crate `{package}` in workspace"),
            };
            rval.push(crate_ref);
        }
        rval
    }

    // LUKE: why does .values().filter() complain ?
    pub fn get_extended_crate_refs(&self, extends: &[CrateName]) -> Vec<&Crate> {
        extends
            .iter()
            .map(|v| {
                self.crate_map
                    .get(v)
                    .unwrap_or_else(|| panic!("Could not load extended crate `{v}`"))
            })
            .collect()
    }

    pub fn get_typescript_crates(&self) -> Vec<&Crate> {
        self.crate_map
            .iter()
            .filter_map(|(_k, v)| {
                if v.mod_config.is_some() || v.app_config.is_some() {
                    Some(v)
                } else {
                    None
                }
            })
            .collect()
    }
}

impl std::fmt::Display for CrateName {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.0)
    }
}

impl std::fmt::Debug for CrateName {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.0)
    }
}

// Implement Deserialize for CrateName
impl<'de> Deserialize<'de> for CrateName {
    fn deserialize<D>(deserializer: D) -> Result<CrateName, D::Error>
    where
        D: serde::Deserializer<'de>,
    {
        let s = String::deserialize(deserializer)?;
        Ok(CrateName(s))
    }
}

impl Application {
    // Get the path of the generated lib.rs code
    pub fn get_libλ_rs_path(&self) -> PathBuf {
        self.abs_path.join("src/libλ.rs")
    }

    pub fn get_libλ_ts_path(&self) -> PathBuf {
        self.abs_path.join("src/libλ.ts")
    }

    pub fn get_extended_crates<'a>(&self, workspace: &'a Workspace) -> Vec<&'a Crate> {
        self.extends_expanded
            .iter()
            .map(|v| {
                workspace
                    .crate_map
                    .get(v)
                    .unwrap_or_else(|| panic!("could not load crate name: {v}"))
            })
            .collect()
    }

    fn extends_expand(top_crate: &Crate, crate_map: &IndexMap<CrateName, Crate>) -> Vec<CrateName> {
        // Recursively build a list of pairs of app names and their extends
        let mut pairs: Vec<(CrateName, CrateName)> = Vec::new();
        Self::_extends_expand_rec(
            &mut pairs,
            top_crate.crate_name.clone(),
            top_crate.extends.clone(),
            crate_map,
        );

        // Create a topological graph
        let mut graph = petgraph::graph::DiGraph::new();

        let app_name_to_node_map = {
            let nameset = {
                let mut set = std::collections::HashSet::new();
                set.insert(top_crate.crate_name.clone());
                for (app_name, extends_app_name) in &pairs {
                    set.insert(app_name.clone());
                    set.insert(extends_app_name.clone());
                }
                set
            };

            let mut map = std::collections::HashMap::new();
            for name in nameset.into_iter().sorted() {
                map.insert(name.clone(), graph.add_node(name.clone()));
            }
            map
        };

        for (app_name, extends_app_name) in &pairs {
            let n1 = app_name_to_node_map.get(app_name).unwrap();
            let n2 = app_name_to_node_map.get(extends_app_name).unwrap();
            graph.add_edge(*n1, *n2, ());
        }

        let sorted_nodes = match petgraph::algo::toposort(&graph, None) {
            Ok(sorted_nodes) => sorted_nodes,
            Err(cycle_node) => {
                panic!("Circular reference detected in `extends` directive: {cycle_node:?}");
            }
        };

        let extends: Vec<_> = sorted_nodes
            .iter()
            .map(|node| {
                let app_name = graph.node_weight(*node).unwrap();
                app_name.to_owned()
            })
            .collect();

        extends
    }

    fn _extends_expand_rec(
        pairs: &mut Vec<(CrateName, CrateName)>,
        app_name: CrateName,
        app_extends: Vec<CrateName>,
        crate_map: &IndexMap<CrateName, Crate>,
    ) {
        for extends_app_name in &app_extends {
            pairs.push((app_name.clone(), extends_app_name.clone()));
        }
        for extends_app_name in &app_extends {
            let member_crate = crate_map
                .get(extends_app_name)
                .ok_or_else(|| {
                    panic!("Error looking up app in workspace.member_map: {extends_app_name}")
                })
                .unwrap();

            if member_crate.mod_config.is_none() {
                panic!(
                    "Error: crate `{}` attempting to extend crate `{}` which does not have `package.metadata.acp.module = {{}}` in `{}/Cargo.toml`",
                    app_name.0, extends_app_name.0, member_crate.rel_path
                );
            }

            Self::_extends_expand_rec(
                pairs,
                member_crate.crate_name.clone(),
                member_crate.extends.clone(),
                crate_map,
            );
        }
    }
}

impl Crate {
    pub fn load(path: &Path, member_path: &str) -> Self {
        let cargo_toml_path = path.join(member_path).join("Cargo.toml");

        // open and read Cargo.toml
        let cargo_toml =
            std::fs::read_to_string(&cargo_toml_path).expect("Error reading Cargo.toml");

        // parse Cargo.toml
        let cargo_toml: crate::parser::crate_cargo_toml::CrateCargoToml =
            match toml::from_str(&cargo_toml) {
                Ok(cargo_toml) => cargo_toml,
                Err(e) => {
                    panic!(
                        "Error parsing Cargo.toml at `{}`\n\n{:#?}",
                        cargo_toml_path.display(),
                        e
                    );
                }
            };

        let crate_to_document_to_path_map = match &cargo_toml.package.metadata.acp.app {
            Some(app) => app
                .docmap
                .iter()
                .map(|(crate_name, doc_name_to_doc_path_map)| {
                    let crate_name = CrateName(crate_name.clone());
                    let doc_name_to_doc_path_map = doc_name_to_doc_path_map
                        .iter()
                        .map(|(document_name, document_path)| {
                            // validate the document name
                            let document_name =
                                DocumentIdent::new(document_name).unwrap_or_else(|e| {
                                    panic!("Error in `{}`: {}", cargo_toml_path.display(), e);
                                });

                            // validate the document path
                            let document_path =
                                DocumentIdent::new(document_path).unwrap_or_else(|e| {
                                    panic!("Error in `{}`: {}", cargo_toml_path.display(), e);
                                });

                            // return the validated document name and path
                            (document_name, document_path)
                        })
                        .collect::<IndexMap<_, _>>();
                    (crate_name, doc_name_to_doc_path_map)
                })
                .collect(),
            None => IndexMap::new(),
        };

        let crate_app = cargo_toml.package.metadata.acp.app.map(|app| CrateApp {
            port: app.port,
            crate_to_document_to_ident: crate_to_document_to_path_map,
        });

        let crate_mod = cargo_toml.package.metadata.acp.module.map(|_| CrateMod {});

        let dependencies_names_set = cargo_toml
            .dependencies
            .keys()
            .cloned()
            .collect::<HashSet<_>>();

        let extends_names_set = cargo_toml
            .package
            .metadata
            .acp
            .extends
            .iter()
            .cloned()
            .collect::<HashSet<_>>();

        Self {
            ident: cargo_toml.package.name.replace('-', "_"),
            crate_name: CrateName(cargo_toml.package.name),
            rel_path: member_path.to_owned(),
            abs_path: path.join(member_path),
            cargo_toml_path,
            version: cargo_toml.package.version,
            edition: cargo_toml.package.edition,
            dependencies: cargo_toml
                .dependencies
                .into_iter()
                .map(|(k, v)| {
                    (
                        k,
                        CrateDependency {
                            workspace: v.workspace,
                            version: v.version,
                            path: v.path,
                        },
                    )
                })
                .collect(),
            extends: cargo_toml
                .package
                .metadata
                .acp
                .extends
                .iter()
                .map(|v| CrateName(v.clone()))
                .collect(),
            dependencies_names_set,
            extends_names_set,
            app_config: crate_app,
            mod_config: crate_mod,

            // the following may be updated later if this is referenced by LOCAL.toml
            in_build: false,
            config: toml::Table::new(),
        }
    }

    /// return a vec of all crates that this crate extends which are also typescript crates
    pub fn get_typescript_crates(&self, workspace: &'static Workspace) -> Vec<&Crate> {
        self.extends
            .iter()
            .map(|v| {
                workspace
                    .crate_map
                    .get(v)
                    .unwrap_or_else(|| panic!("could not load crate name: {v}"))
            })
            // all modules are also typescript crates
            .filter(|v| v.mod_config.is_some())
            .collect()
    }

    pub fn get_entrypoints(&self) -> Vec<PathBuf> {
        let mut ts_paths = Vec::new();

        // this file is ignored by ignore walker, so we all directly
        if self.abs_path.join("src/libλ.ts").exists() {
            ts_paths.push(self.abs_path.join("src/libλ.ts"));
        }

        let src_web_path = self.abs_path.join("src");

        if !src_web_path.exists() {
            return ts_paths;
        }

        let walker = ignore::Walk::new(&src_web_path);

        for entry in walker {
            match entry {
                Ok(entry) => {
                    let p = entry.path();
                    let e = p.extension().unwrap_or_default();
                    if e == "ts" || e == "css" {
                        ts_paths.push(p.to_owned());
                    }
                }
                Err(err) => {
                    panic!("get_entrypoints() error: {err}");
                }
            }
        }

        ts_paths
    }


}
