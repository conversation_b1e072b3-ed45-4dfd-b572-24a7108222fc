use granite_compiler::tokenator::{<PERSON><PERSON><PERSON><PERSON>r, TokenIter};
use syn::{ItemMod, spanned::Spanned};

use super::IOType;

pub fn parse_module_inner(
    input: proc_macro2::TokenStream,
    item_mod: ItemMod,
    api_type: &str,
) -> Result<super::ApiModuleInner, TokenError> {
    
    let api_module_type = match api_type {
        "api" => super::ApiModuleType::WebSocket,
        "function" => super::ApiModuleType::Internal,
        _ => panic!("Unknown api type"),
    };

    // Validate the input is empty
    let mut token_iter = TokenIter::new(input);
    token_iter.step();
    token_iter.get_end()?;

    let mod_span = item_mod.span();
    let mod_attrs = item_mod.attrs;
    let mod_ident = item_mod.ident;

    // Require inline module
    let mod_items_0 = match item_mod.content {
        Some((_, items)) => items,
        None => {
            return Err(TokenError::new(
                mod_span,
                "api modules must be inline (have content between braces)",
            ));
        }
    };

    let mut mod_items = Vec::new();
    let mut input_struct = None;
    let mut output_struct = None;
    let mut call_function = None;

    for item in mod_items_0.into_iter() {
        match item {
            syn::Item::Struct(item_struct) if item_struct.ident == "Input" => {
                input_struct = Some(IOType::Struct(item_struct));
            }
            syn::Item::Enum(item_enum) if item_enum.ident == "Input" => {
                input_struct = Some(IOType::Enum(item_enum));
            }
            syn::Item::Struct(item_struct) if item_struct.ident == "Output" => {
                output_struct = Some(IOType::Struct(item_struct));
            }
            syn::Item::Enum(item_enum) if item_enum.ident == "Output" => {
                output_struct = Some(IOType::Enum(item_enum));
            }
            syn::Item::Fn(item_fn) if item_fn.sig.ident == "call" => {
                call_function = Some(item_fn);
            }
            item => {
                mod_items.push(item);
            }
        }
    }

    let input_type = match input_struct {
        Some(iotype) => super::process_input_type(iotype)?,
        None => {
            return Err(TokenError::new(
                mod_span,
                "api modules must have an `Input` struct or enum",
            ));
        }
    };

    let output_type = match output_struct {
        Some(iotype) => super::process_output_type(iotype)?,
        None => {
            return Err(TokenError::new(
                mod_span,
                "api modules must have an `Output` struct or enum",
            ));
        }
    };

    let call_function = match call_function {
        Some(call_function) => super::process_call_function(call_function)?,
        None => {
            return Err(TokenError::new(
                mod_span,
                "api modules must have a `call` function",
            ));
        }
    };

    Ok(super::ApiModuleInner {
        mod_ident,
        mod_attrs,
        mod_items,
        input_type,
        output_type,
        call_function,
        api_module_type,
    })
}
