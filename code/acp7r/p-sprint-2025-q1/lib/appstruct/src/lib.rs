use approck_redis::RedisCX;

pub fn get_session_key(token: &String) -> String {
    format!("/AppSession/{token}/AuthStack")
}

fn get_infolist_key(token: &String) -> String {
    format!("/AppSession/{token}/_InfoList")
}

fn get_errorlist_key(token: &String) -> String {
    format!("/AppSession/{token}/_ErrorList")
}

// Set a message on InfoList (consumed by AppStruct/Granite)
pub async fn add_info(redis: &mut RedisCX<'_>, token: &String, message: &str) {
    let key = get_infolist_key(token);
    let value = ["str", message];

    let _ = redis.rpush_json(&key, &value).await;
}

// Set an error on ErrorList (consumed by AppStruct/Granite)
pub async fn add_error(redis: &mut RedisCX<'_>, token: &String, error: &str) {
    let key = get_errorlist_key(token);
    let value = ["str", error];

    let _ = redis.rpush_json(&key, &value).await;
}
