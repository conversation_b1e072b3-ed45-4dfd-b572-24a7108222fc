[package]
name = "appstruct"
version = "0.1.0"
edition = "2024"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html
[package.metadata.acp]
module = {}
extends = ["granite"]


[dependencies]
anyhow = { workspace = true }
granite = { workspace = true }
approck-redis = { workspace = true }
hyper = { workspace = true }
maud = { workspace = true }
redis = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
