use crate::{Mark<PERSON>, html};
use granite::DateTimeTz;

pub fn name_value(name: &str, value: Option<DateTimeTz>) -> Markup {
    html! {
        bux-input-datetime name=(name) value=(value.unwrap_or_default()) {
            input type="datetime-local" name=(name) value=[value] {}
        }
    }
}

pub fn bux_input_datetime(name: &str, label: &str, value: Option<DateTimeTz>) -> Markup {
    html! {
        bux-input-datetime name=(name) value=[&value] {
            label { (label) }
            input type="datetime-local" name=(name) value=[value] {}
        }
    }
}

pub fn bux_input_datetime_with_help(
    name: &str,
    label: &str,
    value: Option<DateTimeTz>,
    help: &str,
) -> Markup {
    html! {
        bux-input-datetime name=(name) value=[&value] help=(help) {
            label { (label) }
            input type="datetime-local" name=(name) value=[value] {}
            div.help-text {
                { (help) }
            }
        }
    }
}
