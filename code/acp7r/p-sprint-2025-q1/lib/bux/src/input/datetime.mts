import "./datetime.mcss";

import { type Result, type DateTimeTz, DateTimeTz_decode } from "@granite/lib.mts";
import { NEWID } from "@granite/lib.mts";
import { BuxInput } from "./mod.mts";

class BuxInputDatetime extends BuxInput<DateTimeTz, string | undefined> {
    private $input: HTMLInputElement;
    private $label: HTMLLabelElement | null = null;

    constructor() {
        super();
        this.$label = this.querySelector("label") as HTMLLabelElement | null;
        this.$input = this.querySelector("input") as HTMLInputElement;
        this.$input.type = "datetime-local";

        // feed initial value in through standard mechanism
        if (this.hasAttribute("value")) {
            this.attributeChangedCallback("value", "", this.getAttribute("value") || "");
        }
    }

    connectedCallback(): void {
        const id = NEWID();
        this.$input.id = id;
        if (this.$label) {
            this.$label.htmlFor = id;
        }

        this.$input.addEventListener("input", this.event_on_input.bind(this));
        this.$input.addEventListener("change", this.event_on_change.bind(this));
    }

    disconnectedCallback(): void {
        this.$input.removeEventListener("input", this.event_on_input.bind(this));
        this.$input.removeEventListener("change", this.event_on_change.bind(this));
    }

    on_attr_name(value: string): void {
        this.$input.name = value;
    }

    on_attr_value(value: string): void {
        const result = DateTimeTz_decode(value);
        if ("Ok" in result) {
            this.set_p(result.Ok);
        } else {
            console.error(result.Err);
            this.set_p(new Date());
        }
    }

    on_attr_help(_value: string | null): void {
        //TODO: implement
    }

    // clear custom messages while typing
    private event_on_input(): void {
        this.$input.setCustomValidity("");
    }

    // revalidate once the user has finished typing
    private event_on_change(): void {
        const result = this.get();

        if ("Ok" in result) {
            this.set_e(undefined);
        } else {
            this.set_e(result.Err);
        }

        if (this.on_change) {
            if ("Ok" in result) {
                this.on_change(result.Ok);
            } else {
                this.on_change(undefined);
            }
        }
    }

    public override get(): Result<DateTimeTz, string> {
        const value = this.$input.value.trim();
        if (value === "") {
            return { Ok: new Date() };
        }
        const result = DateTimeTz_decode(value);
        if ("Ok" in result) {
            return result;
        } else {
            return { Err: "invalid datetime input" };
        }
    }

    public override set_p(value: DateTimeTz | undefined) {
        this.set_e(undefined);
        if (value === undefined) {
            this.$input.value = "";
        } else {
            this.$input.value = value.toISOString().slice(0, 16);
        }
    }

    public override set_e(value: string | null | undefined): void {
        this.$input.setCustomValidity(value || "");
        this.$input.title = value || "";
    }

    public override get_e(): string | undefined {
        return this.$input.validationMessage || undefined;
    }
}

globalThis.customElements.define("bux-input-datetime", BuxInputDatetime);
export default BuxInputDatetime;
