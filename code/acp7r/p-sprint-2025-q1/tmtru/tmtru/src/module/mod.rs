
mod api_openai;
mod approck_postgres;
mod approck_redis;
mod approck_server;
mod auth_fence;
mod bux;
mod granite;
mod tmtru_admin;
mod tmtru_public;
mod tmtru_zero;

impl crate::App for crate::AppStruct {}

impl crate::Identity for crate::IdentityStruct {
    fn web_usage(&self) -> bool {
        ::auth_fence::Identity::is_logged_in(self)
    }
    fn api_usage(&self) -> bool {
        ::auth_fence::Identity::is_logged_in(self)
    }
}

impl crate::Document for crate::DocumentStruct {}
