bux::document! {

    pub struct Document {}

    impl Document {
        pub fn new(
            app: &'static crate::AppStruct,
            identity: &crate::IdentityStruct,
            req: &approck::server::Request,
        ) -> Self {
            use auth_fence::App;
            use bux::document::Cliffy;
            use bux::document::{Base, Nav2, Nav2Dropdown, Nav2Logo};

            let mut this = Self {
                ..Default::default()
            };

            // Base setup
            this.set_uri(req.path());
            this.set_title("tmtru"); // default title
            this.set_site_name("tmtru");
            this.set_owner("AppCove, Inc");
            this.add_logo("/", "https://asset7.net/tmtru/tmtru-logo-white.svg");

            // Disable PageNav
            this.hide_page_nav();

            // Add Google Fonts
            this.add_head(maud::html!(
                link href="https://fonts.googleapis.com/css2?family=Figtree:ital,wght@0,300..900;1,300..900&display=swap');" rel="stylesheet";
            ));

            // Nav2 setup
            this.set_identity(identity);


            // Add Get Help link
            this.add_nav2_menu_link("Get Help", "/", "");

            match auth_fence::Identity::is_logged_in(identity) {
                true => {
                    if let Some(name) = bux::Identity::name(identity) {
                        let user_menu: &mut Nav2Dropdown;
                        if let Some(avatar_uri) = bux::Identity::avatar_uri(identity) {
                            let user_logo = Nav2Logo { url: avatar_uri, alt: None };
                            user_menu = this.add_nav2_menu_dropdown(&name, "user-menu", Some(user_logo), None);
                        } else {
                            user_menu = this.add_nav2_menu_dropdown(&name, "user-menu", None, None);
                        }
                        user_menu.add_link("Dashboard", "/dashboard", None);
                        user_menu.add_link("My Account", "/myaccount/", None);
                        user_menu.add_span(format!("Logged in as {name}").as_str());
                        user_menu.add_link("Logout", auth_fence::App::logout_url(app), None);
                    };
                }
                false => {
                    this.add_nav2_menu_link("Login", app.login_url(), "");
                }
            }

            this
        }
    }

    impl bux::document::Base for Document {
        fn render_body(&self) -> maud::Markup {
            use bux::document::{Base, Nav1, Nav2, Cliffy};
            use maud::{html};

            html!(
                layout-wrapper-outer.cliffy {
                    layout-wrapper-inner {
                        @if !self.is_headerbar_empty() {
                            header-bar id="header-bar" {
                                (self.render_headerbar_inner())
                            }
                        }
                        nav-wrapper {
                            content-container.fluid {
                                nav-controls {
                                    (self.render_go_back_button())
                                    (self.render_breadcrumb())
                                }
                                nav-header id="horizontal-nav-header" {
                                    a href=(Base::logo_href(self)) aria-label=(format!("Home - {}", Base::site_name(self))) {
                                        img src=(Base::logo_src(self)) alt=(format!("{} Logo", Base::site_name(self))) width="100" height="50" {}
                                    }
                                }
                                (Nav1::render_nav1(self))
                                (Nav2::render(self))
                            }
                        }
                        (self.render_content_wrapper())
                    }
                }
            )
        }
    }

    impl bux::document::Nav1 for Document {}
    impl bux::document::Nav2 for Document {}
    impl bux::document::PageNav for Document {}
    impl bux::document::FooterSocial for Document {}
    impl bux::document::FooterLinks for Document {}
    impl bux::document::Cliffy for Document {}
    impl api_openai::Document for Document {}


}
