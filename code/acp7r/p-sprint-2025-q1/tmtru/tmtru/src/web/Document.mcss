
/*********************************************************/
/* CSS Variable Overrides */

body {

    /* site logo */
    --cliffy-pnav-logo-width: 100px;

    /* nav-wrapper */
    --bux-nav-wrap-bg-color: #066eb6;

    /* primary navigation */
    --cliffy-pnav-bg-color: #fff;

    /* heirarchical navigation */
    --pnav-a-color: #0b233a;
    --pnav-a-hover-color: #0b233a;
    --pnav-a-hover-bg-color: #ececec;
    --pnav-a-fw: 500;
    --pnav-a-hover-fw: 500;
    --pnav-a-selected-color: #0b233a;
    --pnav-a-selected-bg-color: #cde2f0;
    --pnav-a-selected-fw: 600;
    --pnav-group-name-fw: 600;
    --pnav-group-name-selected-top-border: #cde2f0;
    --pnav-group-name-selected-bottom-border: #cde2f0;
    --pnav-group-name-hover-top-border: #ececec;
    --pnav-group-name-hover-bottom-border: #ececec;

    /* split button */
    --cliffy-splitbtn-a-color: #066eb6;
    --cliffy-splitbtn-a-hover-color: #066eb6;
    --cliffy-splitbtn-a-bg-color: #fff;
    --cliffy-splitbtn-a-border-color: #fff;
    --cliffy-splitbtn-btn-bg-color: #055892;
    --cliffy-splitbtn-btn-border-color: #055892;

    /* page navigation */
    --bux-page-nav-bg-color: transparent;
    --bux-page-nav-a-bg-color: transparent;
    --bux-page-nav-a-border: transparent;
    --bux-page-nav-a-color: #0b233a;
    --bux-page-nav-a-hover-color: #3e3cff;
    --bux-page-nav-a-fw: 600;
    --bux-page-nav-a-selected-fw: 600;
    --bux-page-nav-a-selected-td: none;
    --bux-page-nav-a-selected-bg-color: #3e3cff;
    --bux-page-nav-a-selected-hover-color: #fff;
    --bux-page-nav-a-selected-border-color: #3e3cff;

}

/*********************************************************/

body {
    font-family: 'Figtree', sans-serif;
    background-color: #ececec;
    background-image: url('https://asset7.net/tmtru/wavy-lines-white-v4.svg');
    background-repeat: no-repeat;
    background-size: cover;
    background-attachment: fixed;
    background-position: center;
}

/* Replace the left padding on the nav-wrapper container */
.cliffy nav-wrapper {
    
    content-container {

        @media (min-width: 992px) {
            padding-left: 1rem;
        }
    }
}

/*********************************************************/
/* Flex wrapper for the go back button and breadcrumb */

nav-wrapper {

    nav-controls {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

}

/*********************************************************/
/* Base styles for the primary/secondary navigation */

nav-wrapper {
    margin-left: -250px;

    content-container {

        @media (min-width: 992px) {
            grid-template-columns: 1fr auto 1fr;
        }

        > nav-header {
            @media (min-width: 992px) {
                grid-column: 2;
            }
        }
    }
}

/*********************************************************/
/* Go Back Button */

.cliffy nav-wrapper {

    split-button {

        @media (min-width: 992px) {
            grid-column: 1;
        }
    }
}

/*********************************************************/
/* Styles specific to the vertical navigation */

@media (min-width: 992px){

    .cliffy nav#primary-navigation {

        menu-wrapper-outer {
            top: 55px;
            z-index: 999;
            
            menu-wrapper-inner {

                @media (min-width: 992px) {
                    background-image: url('https://asset7.net/tmtru/wavy-lines-gray-v3.svg');
                    background-repeat: no-repeat;
                    background-size: cover;
                    background-attachment: fixed;
                    background-position: center;
                    background-origin: content-box;
                }
            }
        }
    }
}

/*********************************************************/
/* These styles control the nav-header display */

.cliffy nav-header#horizontal-nav-header {
    /* Hide at the large (992px) breakpoint */
    @media (min-width: 992px) {
        display: block;
    }
}

.cliffy nav-header#vertical-nav-header {
    @media (min-width: 992px) {
        display: none; 
    }
}

/*********************************************************/
