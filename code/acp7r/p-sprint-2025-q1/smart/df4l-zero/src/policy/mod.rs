#![allow(dead_code)]

/*
In order to project values on a monthly basis, we need these inputs

    Formula Version
    this may change from time to time, so v1, v2, v3
    Starting Year and Month
    Starting Client Age
    Client Gender
    Premium Base
    Premium PUA
    Face Value (will be divided by 1000 to be units of insurance)
    Annual Premium Rate per Unit (premium)
    Current Cash Value
    Current PUA
    Current Year and Month
    LIMIT, OFFSET
    default to 0, 360


A list of all months from the starting year to the end
    Row Number
    Year
    Month
    Base Policy Cash Value
    PUA Cash Value
    Total Cash Value = the sum of the above two
    Amount available to borrow = 90% of that



*/

mod cash_value_base;
mod pua;
mod units_of_insurance;

use self::cash_value_base::get_cash_value_base_at_month;
use self::pua::{get_pua_balance_growth, get_pua_contribution_fee};
use self::units_of_insurance::get_units_of_insurance;
use crate::types::Gender;

use granite::{DateUtc, Decimal, age_in_years_projected};

#[derive(Debug)]
pub struct PuaSplit {
    premium_percentage: u8,
    pua_percentage: u8,
}

impl std::fmt::Display for PuaSplit {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(
            f,
            "{}% Premium / {}% PUA",
            self.premium_percentage, self.pua_percentage
        )
    }
}

impl PuaSplit {
    /// used to send to icover... they want 100/0 to be null
    pub fn icover_identifier(&self) -> Option<String> {
        if self.pua_percentage == 0 {
            None
        } else {
            Some(format!(
                "{}/{}",
                self.premium_percentage, self.pua_percentage
            ))
        }
    }

    pub fn new(base_percentage: u8, pua_percentage: u8) -> Self {
        assert_eq!(base_percentage + pua_percentage, 100);
        Self {
            premium_percentage: base_percentage,
            pua_percentage,
        }
    }

    pub fn new_100_0() -> Self {
        Self {
            premium_percentage: 100,
            pua_percentage: 0,
        }
    }

    pub fn new_max(gender: &Gender, birth_date: DateUtc) -> Self {
        crate::policy::pua::calculate_max_pua(birth_date, gender)
    }

    pub fn pua_percentage(&self) -> Decimal {
        Decimal::new(self.pua_percentage as i64, 2)
    }
}

#[derive(Copy, Clone)]
pub enum Version {
    V202506,
}

/// This struct is used to provide input to the number cruncher for the policy simulation
pub struct CrunchInput {
    pub version: Version,

    pub insured_birth_date: DateUtc,
    pub insured_gender: Gender,

    pub start_date: DateUtc,

    /// Total monthly premium amount
    pub monthly_premium_amount: Decimal,

    /// Whether this is insurance-only (no PUA)
    /// None or Max
    pub pua_split_string: String,

    /// One time PUA contribution
    pub initial_pua_contribution: Decimal,
}

#[derive(Debug)]
pub struct CrunchOutput {
    pub month_list: Vec<Month>,
    pub units_of_insurance: Decimal,

    /// Monthly premium amount is the same as (monthly_premium_base_amount + monthly_premium_pua_amount)
    pub monthly_premium_amount: Decimal,

    /// Monthly premium base amount (calculated from split)
    pub monthly_premium_base_amount: Decimal,
    /// Monthly premium PUA amount (calculated from split)
    pub monthly_premium_pua_amount: Decimal,
    /// The PUA split used for calculations and iCover
    pub pua_split: PuaSplit,
}

#[derive(Debug)]
pub struct Month {
    pub date: DateUtc,
    pub month_number: u32,
    pub cash_value_base: Decimal,
    pub cash_value_pua: Decimal,
    pub cash_value_total: Decimal,
    pub amount_available_to_borrow: Decimal,
}

impl CrunchInput {
    pub fn crunch(&self) -> Result<CrunchOutput, String> {
        // calculate insured_age
        let initial_insured_age = age_in_years_projected(self.insured_birth_date, self.start_date);

        // Calculate PUA split based on age, gender, and insurance_only flag
        let pua_split = match self.pua_split_string.as_ref() {
            "None" => PuaSplit::new_100_0(),
            "Max" => PuaSplit::new_max(&self.insured_gender, self.insured_birth_date),
            _ => return Err("PUA Split setting is required.".to_string()),
        };

        // Calculate base and PUA amounts from total premium
        let monthly_premium_base_amount =
            self.monthly_premium_amount * Decimal::new(pua_split.premium_percentage as i64, 2);
        let monthly_premium_pua_amount =
            self.monthly_premium_amount * Decimal::new(pua_split.pua_percentage as i64, 2);

        // Calculate the annual premium amount for units of insurance calculation
        let annual_premium_amount = monthly_premium_base_amount * Decimal::new(12, 0);

        // validate age is between 25 and 60
        if !(25..=60).contains(&initial_insured_age) {
            return Err("Insured age must be between 25 and 60".to_string());
        }

        // get the units of insurance
        let units_of_insurance = get_units_of_insurance(
            &self.version,
            initial_insured_age,
            self.insured_gender,
            annual_premium_amount,
        )?;

        // calulate base policy cash value
        let mut month_list = Vec::new();
        let mut cash_value_pua = self.initial_pua_contribution;

        for month_number in 0..360 {
            let months = chrono::Months::new(month_number);
            let projected_date = match self.start_date.checked_add_months(months) {
                Some(d) => d,
                None => return Err("Date overflow".to_string()),
            };
            let projected_insured_age = age_in_years_projected(self.insured_birth_date, projected_date);

            // Paid up additions stop after 10 years
            if (0..=120).contains(&month_number) {
                let pua_contribution_fee =
                    get_pua_contribution_fee(&self.version, monthly_premium_pua_amount)?;
                let pua_contribution = monthly_premium_pua_amount - pua_contribution_fee;
                cash_value_pua += pua_contribution;

                let pua_balance_growth = get_pua_balance_growth(
                    &self.version,
                    cash_value_pua,
                    projected_insured_age,
                    self.insured_gender,
                )?;

                // Increment the cash value PUA with the growth factor
                cash_value_pua += pua_balance_growth;
            }

            // Calculate the cash value BASE
            let cash_value_base = get_cash_value_base_at_month(
                &self.version,
                projected_insured_age,
                self.insured_gender,
                month_number,
                units_of_insurance,
            )?;

            // build the month struct
            let month = Month {
                date: projected_date,
                month_number,
                cash_value_base,
                cash_value_pua,
                cash_value_total: cash_value_base + cash_value_pua,
                amount_available_to_borrow: (cash_value_base + cash_value_pua)
                    * Decimal::new(90, 2),
            };

            month_list.push(month);
        }

        let monthly_premium_amount = monthly_premium_base_amount + monthly_premium_pua_amount;

        Ok(CrunchOutput {
            month_list,
            units_of_insurance,
            monthly_premium_amount,
            monthly_premium_base_amount,
            monthly_premium_pua_amount,
            pua_split,
        })
    }
}

//-------------------------------------------------------------------------------------------------
// PUA Validation Structures (following the same pattern as debt validation)

pub struct PuaInput {
    pub pua_split: Option<String>,
    pub pua_contribution: Option<Decimal>,
    pub monthly_total_premium: Option<Decimal>,
    pub insured_gender: Option<Gender>,
    pub insured_birth_date: Option<DateUtc>,
}

pub struct Pua {
    pub pua_split: PuaSplit,
    pub pua_contribution: Decimal,
    pub monthly_total_premium: Decimal,
    pub monthly_pua_premium: Decimal,
    pub monthly_base_premium: Decimal,
}

pub struct PuaError {
    pub pua_split: Option<String>,
    pub pua_contribution: Option<String>,
    pub monthly_total_premium: Option<String>,
    pub insured_gender: Option<String>,
    pub insured_birth_date: Option<String>,
}

impl std::fmt::Display for PuaError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        writeln!(f, "PUA Errors:")?;
        if let Some(pua_split) = &self.pua_split {
            writeln!(f, "  pua_split: {pua_split}")?;
        }
        if let Some(pua_contribution) = &self.pua_contribution {
            writeln!(f, "  pua_contribution: {pua_contribution}")?;
        }
        if let Some(monthly_total_premium) = &self.monthly_total_premium {
            writeln!(f, "  monthly_total_premium: {monthly_total_premium}")?;
        }
        if let Some(insured_gender) = &self.insured_gender {
            writeln!(f, "  insured_gender: {insured_gender}")?;
        }
        if let Some(insured_birth_date) = &self.insured_birth_date {
            writeln!(f, "  insured_birth_date: {insured_birth_date}")?;
        }
        writeln!(f)?;
        Ok(())
    }
}

impl PuaInput {
    pub fn validate(&self) -> Result<Pua, PuaError> {
        let mut error = PuaError {
            pua_split: None,
            pua_contribution: None,
            monthly_total_premium: None,
            insured_gender: None,
            insured_birth_date: None,
        };

        let (
            pua_split,
            pua_contribution,
            monthly_total_premium,
            insured_gender,
            insured_birth_date,
        ) = match (
            &self.pua_split,
            self.pua_contribution,
            self.monthly_total_premium,
            self.insured_gender,
            self.insured_birth_date,
        ) {
            (
                Some(pua_split),
                Some(contribution),
                Some(monthly_total_premium),
                Some(insured_gender),
                Some(insured_birth_date),
            ) => (
                pua_split,
                contribution.round_dp(2),
                monthly_total_premium.round_dp(2),
                insured_gender,
                insured_birth_date,
            ),
            (
                pua_split,
                pua_contribution,
                monthly_total_premium,
                insured_gender,
                insured_birth_date,
            ) => {
                error.pua_split = pua_split
                    .is_none()
                    .then(|| "PUA split is required.".to_string());
                error.pua_contribution = pua_contribution
                    .is_none()
                    .then(|| "PUA contribution is required (it can be $0).".to_string());
                error.monthly_total_premium = monthly_total_premium
                    .is_none()
                    .then(|| "Monthly Total Premium is required (it can be $0).".to_string());
                error.insured_gender = insured_gender
                    .is_none()
                    .then(|| "Insured gender is required.".to_string());
                error.insured_birth_date = insured_birth_date
                    .is_none()
                    .then(|| "Insured birth date is required.".to_string());
                return Err(error);
            }
        };

        let pua_split = match pua_split.as_str() {
            "None" => PuaSplit::new_100_0(),
            "Max" => PuaSplit::new_max(&insured_gender, insured_birth_date),
            _ => {
                error.pua_split = Some("Invalid PUA split.".to_string());
                return Err(error);
            }
        };

        if pua_contribution < Decimal::ZERO || pua_contribution > Decimal::new(1000000, 0) {
            error.pua_contribution = Some("Must be between $0 and $1,000,000.".to_string());
        }

        if monthly_total_premium < Decimal::ZERO || monthly_total_premium > Decimal::new(1000000, 0)
        {
            error.monthly_total_premium = Some("Must be between $0 and $1,000,000.".to_string());
        }

        if error.pua_split.is_some()
            || error.pua_contribution.is_some()
            || error.monthly_total_premium.is_some()
        {
            return Err(error);
        }

        let pua_percentage = pua_split.pua_percentage();
        let monthly_pua_premium = (monthly_total_premium * pua_percentage).round_dp(2);
        let monthly_base_premium = monthly_total_premium - monthly_pua_premium;

        Ok(Pua {
            pua_split,
            pua_contribution,
            monthly_total_premium,
            monthly_pua_premium,
            monthly_base_premium,
        })
    }
}
