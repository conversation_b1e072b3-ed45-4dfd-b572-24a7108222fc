//! This file is powered off of the df4l.client table
//! SECON checks must be done before calling this functionality.

use granite::Decimal;
use granite::return_invalid_data;

pub type ClientType = crate::types::ClientType;

//-------------------------------------------------------------------------------------------------

pub struct Wizard {
    pub client_uuid: granite::Uuid,
    pub advisor_uuid: granite::Uuid,
    pub client_type: ClientType,
    pub advisor_result: Result<AdvisorOutput, AdvisorError>,
    pub contact_partial: ContactInput,
    pub contact_result: Result<Contact, ContactError>,
    pub crs_result: Result<Crs, CrsError>,
    pub debts_input: Result<Vec<DebtRow>, String>,
    pub debts_result: Result<Debts, DebtsError>,
    pub budget_input: BudgetInput,
    pub budget_result: Result<BudgetOutput, BudgetError>,
    pub pua_input: PuaInput,
    pub pua_result: Result<Pua, PuaError>,
    pub icover_result: Result<ICoverOutput, Vec<ICoverErrorMessage>>,

    pub contact_complete: bool,
    pub crs_complete: bool,
    pub debt_complete: bool,
    pub budget_complete: bool,
    pub pua_complete: bool,
    pub report_complete: bool,
    pub icover_complete: bool,
}

impl Wizard {
    pub async fn load(
        app: &impl crate::App,
        db: &impl approck_postgres::DB,
        client_uuid: granite::Uuid,
    ) -> granite::Result<Wizard> {
        // query to get all the data
        let row = granite::pg_row!(
            db = db;
            args = {
                $client_uuid: &client_uuid,
            };
            row = {
                advisor_uuid: Uuid,
                advisor_identity_uuid: Uuid,
                advisor_gbu_esid: Option<String>,
                advisor_gbu_esid_valid: bool,
                advisor_first_name: String,
                advisor_last_name: String,
                advisor_address_1: Option<String>,
                advisor_address_2: Option<String>,
                advisor_city: Option<String>,
                advisor_state: Option<String>,
                advisor_zip: Option<String>,
                advisor_email: Option<String>,
                advisor_phone: Option<String>,
                advisor_statelic: Vec<String>,
                client_type_str: String,
                first_name: Option<String>,
                last_name: Option<String>,
                email: Option<String>,
                phone: Option<String>,
                address1: Option<String>,
                address2: Option<String>,
                city: Option<String>,
                state: Option<String>,
                zip: Option<String>,
                gender: Option<String>,
                birth_date: Option<DateUtc>,
                budget_extra_debts: Option<Decimal>,
                budget_extra_savings: Option<Decimal>,
                budget_extra_retirement: Option<Decimal>,
                budget_extra_surplus: Option<Decimal>,
                budget_pua_split: Option<String>,
                budget_pua_contribution: Option<Decimal>,
                policy_total_premium: Option<Decimal>,
                api_crs_user_enabled_applicant: Option<bool>,
                api_crs_user_verified_applicant: bool,
                api_crs_user_enabled_spouse: Option<bool>,
                api_crs_user_verified_spouse: bool,
            };
            SELECT
                advisor.advisor_uuid,
                advisor.identity_uuid,
                advisor.gbu_advisor_esid,
                EXISTS (
                    SELECT FROM df4l.gbu_agent
                    WHERE gbu_agent_esid = advisor.gbu_advisor_esid
                    AND active
                ) AS advisor_gbu_esid_valid,
                advisor.first_name,
                advisor.last_name,
                advisor.address1,
                advisor.address2,
                advisor.city,
                advisor.state,
                advisor.zip,
                advisor.email,
                advisor.phone,
                COALESCE((
                    SELECT
                        array_agg(state_code)
                    FROM
                        df4l.advisor_statelic
                    WHERE
                        advisor_uuid = advisor.advisor_uuid
                ), ARRAY[]::text[]) AS advisor_statelic,
                client.client_type::text AS client_type_str,
                client.first_name,
                client.last_name,
                client.email,
                client.phone,
                client.address1,
                client.address2,
                client.city,
                client.state,
                client.zip,
                client.gender::text AS gender,
                client.birth_date,
                client.budget_extra_debts,
                client.budget_extra_savings,
                client.budget_extra_retirement,
                client.budget_extra_surplus,
                client.budget_pua_split,
                client.budget_pua_contribution,
                client.policy_total_premium,

                // Nullable (null means user didn't decide)
                client.api_crs_user_enabled_applicant,

                // Not null - they are either verified with a last report (true) or not (false)
                EXISTS (
                    SELECT
                    FROM api_crs.user
                    WHERE user.api_crs_user_uuid = client.api_crs_user_uuid_applicant
                    AND verify_ts IS NOT NULL AND last_report_esid IS NOT NULL
                ) AS api_crs_user_verified_applicant,

                // Nullable (null means user didn't decide)
                client.api_crs_user_enabled_spouse,

                // Not null - they are either verified with a last report (true) or not (false)
                EXISTS (
                    SELECT
                    FROM api_crs.user
                    WHERE user.api_crs_user_uuid = client.api_crs_user_uuid_spouse
                    AND verify_ts IS NOT NULL AND last_report_esid IS NOT NULL
                ) AS api_crs_user_verified_spouse
            FROM
                df4l.client
                INNER JOIN df4l.advisor ON advisor.advisor_uuid = client.advisor_uuid
            WHERE
                client.client_uuid = $client_uuid
        )
        .await?;

        let advisor_partial = AdvisorPartial {
            advisor_uuid: row.advisor_uuid,
            gbu_esid: row.advisor_gbu_esid,
            gbu_esid_valid: row.advisor_gbu_esid_valid,
            first_name: row.advisor_first_name,
            last_name: row.advisor_last_name,
            address1: row.advisor_address_1,
            address2: row.advisor_address_2,
            city: row.advisor_city,
            state: row.advisor_state,
            zip: row.advisor_zip,
            email: row.advisor_email,
            phone: row.advisor_phone,
            statelic: row.advisor_statelic,
        };

        let advisor_result = advisor_partial.validate();

        let contact_gender = match row.gender.as_deref() {
            Some("Male") => Some(crate::types::Gender::Male),
            Some("Female") => Some(crate::types::Gender::Female),
            _ => None,
        };

        let contact_partial = ContactInput {
            first_name: row.first_name,
            last_name: row.last_name,
            email: row.email,
            phone: row.phone,
            address1: row.address1,
            address2: row.address2,
            city: row.city,
            state: row.state,
            zip: row.zip,
            gender: row.gender,
            birth_date: row.birth_date,
        };

        let contact_result = contact_partial.validate();

        let debt_inputs = crate::client::debt::get_client_debts_merged(app, client_uuid).await?;

        let debt_result = match &debt_inputs {
            Ok(debts) => validate_debts_input(debts),
            Err(_) => Err(DebtsError {
                outer: "There were errors in your debt inputs.".to_string(),
                inner: Vec::new(),
            }),
        };

        let budget_input = BudgetInput {
            budget_extra_debts: row.budget_extra_debts,
            budget_extra_savings: row.budget_extra_savings,
            budget_extra_retirement: row.budget_extra_retirement,
            budget_extra_surplus: row.budget_extra_surplus,
            budget_pua_split: row.budget_pua_split.clone(),
            budget_pua_contribution: row.budget_pua_contribution,
        };

        let budget_result = budget_input.validate();

        let pua_input = PuaInput {
            pua_split: row.budget_pua_split,
            pua_contribution: row.budget_pua_contribution,
            monthly_total_premium: row.policy_total_premium,
            insured_gender: contact_gender,
            insured_birth_date: row.birth_date,
        };

        let pua_result = pua_input.validate();

        // Determine client type for validation logic
        let client_type = match row.client_type_str.as_str() {
            "DebtManagement" => ClientType::DebtManagement,
            "PolicyOnly" => ClientType::PolicyOnly,
            _ => return_invalid_data!("Unexpected client type: {}", row.client_type_str),
        };

        let icover_result = {
            let mut errors = Vec::new();

            if let Err(ref e) = advisor_result {
                errors.push(ICoverErrorMessage {
                    message: "Your advisor setup is incomplete.".to_string(),
                    url: None,
                });
                if let Some(ref e) = e.gbu_esid {
                    errors.push(ICoverErrorMessage {
                        message: e.to_owned(),
                        url: Some(crate::ml_myaccount_advisor_gbu(row.advisor_uuid)),
                    });
                }
                if let Some(ref e) = e.first_name {
                    errors.push(ICoverErrorMessage {
                        message: e.to_owned(),
                        url: None,
                    });
                }
                if let Some(ref e) = e.last_name {
                    errors.push(ICoverErrorMessage {
                        message: e.to_owned(),
                        url: None,
                    });
                }
                if let Some(ref e) = e.email {
                    errors.push(ICoverErrorMessage {
                        message: e.to_owned(),
                        url: Some(crate::ml_myaccount_advisor_email(row.advisor_uuid)),
                    });
                }
                if let Some(ref e) = e.phone {
                    errors.push(ICoverErrorMessage {
                        message: e.to_owned(),
                        url: Some(crate::ml_myaccount_advisor_phone(row.advisor_uuid)),
                    });
                }
                if e.address1.is_some()
                    || e.address2.is_some()
                    || e.city.is_some()
                    || e.state.is_some()
                    || e.zip.is_some()
                {
                    errors.push(ICoverErrorMessage {
                        message: "Full Address is required.".to_string(),
                        url: Some(crate::ml_myaccount_advisor_address(row.advisor_uuid)),
                    });
                }
                if let Some(ref e) = e.statelic {
                    errors.push(ICoverErrorMessage {
                        message: e.to_owned(),
                        url: Some(crate::ml_myaccount_advisor_statelic(row.advisor_uuid)),
                    });
                }
            }

            if contact_result.is_err() {
                errors.push(ICoverErrorMessage {
                    message: "Your client's personal information setup is incomplete.".to_string(),
                    url: Some(crate::ml_advisor_client_wizard_contact(
                        row.advisor_uuid,
                        client_uuid,
                    )),
                });
            }

            // Only check debt and budget for DebtManagement clients
            match client_type {
                ClientType::DebtManagement => {
                    if debt_result.is_err() {
                        errors.push(ICoverErrorMessage {
                            message: "Your client's debt information setup is incomplete."
                                .to_string(),
                            url: Some(crate::ml_advisor_client_wizard_debt(
                                row.advisor_uuid,
                                client_uuid,
                            )),
                        });
                    }

                    if budget_result.is_err() {
                        errors.push(ICoverErrorMessage {
                            message: "Your client's budget information setup is incomplete."
                                .to_string(),
                            url: Some(crate::ml_advisor_client_wizard_budget(
                                row.advisor_uuid,
                                client_uuid,
                            )),
                        });
                    }
                }
                ClientType::PolicyOnly => {
                    // For PolicyOnly clients, check PUA instead of debt/budget
                    if pua_result.is_err() {
                        errors.push(ICoverErrorMessage {
                            message: "Your client's PUA setup is incomplete.".to_string(),
                            url: Some(crate::ml_advisor_client_wizard_policy(
                                row.advisor_uuid,
                                client_uuid,
                            )),
                        });
                    }
                }
            }

            if let Err(ref e) = pua_result {
                if let Some(ref e) = e.pua_split {
                    errors.push(ICoverErrorMessage {
                        message: format!("PUA Split: {e}"),
                        url: None,
                    });
                }
                if let Some(ref e) = e.pua_contribution {
                    errors.push(ICoverErrorMessage {
                        message: format!("PUA Contribution: {e}"),
                        url: None,
                    });
                }
                if let Some(ref e) = e.monthly_total_premium {
                    errors.push(ICoverErrorMessage {
                        message: format!("Monthly Total Premium: {e}"),
                        url: None,
                    });
                }
                if let Some(ref e) = e.insured_gender {
                    errors.push(ICoverErrorMessage {
                        message: format!("Insured Gender: {e}"),
                        url: None,
                    });
                }
                if let Some(ref e) = e.insured_birth_date {
                    errors.push(ICoverErrorMessage {
                        message: format!("Insured Birth Date: {e}"),
                        url: None,
                    });
                }
            }

            if !errors.is_empty() {
                Err(errors)
            } else {
                Ok(ICoverOutput {
                    icover_url: app.icover_client_login_url(client_uuid),
                })
            }
        };

        let crs_result = CrsInput {
            applicant_enabled: row.api_crs_user_enabled_applicant,
            applicant_verified: row.api_crs_user_verified_applicant,
            spouse_enabled: row.api_crs_user_enabled_spouse,
            spouse_verified: row.api_crs_user_verified_spouse,
        }
        .validate();

        let contact_complete = contact_result.is_ok();
        let crs_complete = crs_result.is_ok();
        let debt_complete = debt_result.is_ok();
        let budget_complete = budget_result.is_ok();
        let pua_complete = pua_result.is_ok();
        let report_complete = false;
        let icover_complete = icover_result.is_ok();

        Ok(Wizard {
            client_uuid,
            advisor_uuid: row.advisor_uuid,
            client_type,
            advisor_result,
            contact_partial,
            contact_result,
            crs_result,
            debts_input: debt_inputs,
            debts_result: debt_result,
            budget_input,
            budget_result,
            pua_input,
            pua_result,
            icover_result,
            contact_complete,
            crs_complete,
            debt_complete,
            budget_complete,
            pua_complete,
            report_complete,
            icover_complete,
        })
    }

    // TODO:DF4L: we need to know when the policy starts!
    pub fn to_policy_output(&self) -> Result<crate::policy::CrunchOutput, String> {
        let contact_ok = match &self.contact_result {
            Ok(contact) => contact,
            Err(_) => return Err("Contact information is incomplete.".to_string()),
        };

        let budget_ok = match &self.budget_result {
            Ok(budget) => budget,
            Err(_) => return Err("Budget information is incomplete.".to_string()),
        };

        let crunch_input = crate::policy::CrunchInput {
            version: crate::policy::Version::V202506,
            insured_birth_date: contact_ok.birth_date,
            insured_gender: contact_ok.gender,
            start_date: granite::current_date(),
            monthly_premium_amount: budget_ok.budget_extra_surplus,
            pua_split_string: budget_ok.budget_pua_split.clone(),
            initial_pua_contribution: budget_ok.budget_pua_contribution,
        };

        crunch_input.crunch()
    }
}

#[granite::gtype]
pub struct AdvisorOutput {
    pub advisor_uuid: Uuid,
    pub gbu_esid: String,
    pub first_name: String,
    pub last_name: String,
    pub address1: String,
    pub address2: Option<String>,
    pub city: String,
    pub state: String,
    pub zip: String,
    pub email: String,
    pub phone: String,
    pub statelic: Vec<String>,
}

#[granite::gtype]
pub struct AdvisorPartial {
    pub advisor_uuid: Uuid,
    pub gbu_esid: Option<String>,
    pub gbu_esid_valid: bool,
    pub first_name: String,
    pub last_name: String,
    pub address1: Option<String>,
    pub address2: Option<String>,
    pub city: Option<String>,
    pub state: Option<String>,
    pub zip: Option<String>,
    pub email: Option<String>,
    pub phone: Option<String>,
    pub statelic: Vec<String>,
}

#[granite::gtype]
#[derive(Debug)]
pub struct AdvisorError {
    pub advisor_uuid: Uuid,
    pub gbu_esid: Option<String>,
    pub first_name: Option<String>,
    pub last_name: Option<String>,
    pub address1: Option<String>,
    pub address2: Option<String>,
    pub city: Option<String>,
    pub state: Option<String>,
    pub zip: Option<String>,
    pub email: Option<String>,
    pub phone: Option<String>,
    pub statelic: Option<String>,
}

impl std::fmt::Display for AdvisorError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        writeln!(f, "Advisor Errors:")?;
        if let Some(gbu_esid) = &self.gbu_esid {
            writeln!(f, "  gbu_esid: {gbu_esid}")?;
        }
        if let Some(first_name) = &self.first_name {
            writeln!(f, "  first_name: {first_name}")?;
        }
        if let Some(last_name) = &self.last_name {
            writeln!(f, "  last_name: {last_name}")?;
        }
        if let Some(address1) = &self.address1 {
            writeln!(f, "  address1: {address1}")?;
        }
        if let Some(address2) = &self.address2 {
            writeln!(f, "  address2: {address2}")?;
        }
        if let Some(city) = &self.city {
            writeln!(f, "  city: {city}")?;
        }
        if let Some(state) = &self.state {
            writeln!(f, "  state: {state}")?;
        }
        if let Some(zip) = &self.zip {
            writeln!(f, "  zip: {zip}")?;
        }
        if let Some(email) = &self.email {
            writeln!(f, "  email: {email}")?;
        }
        if let Some(phone) = &self.phone {
            writeln!(f, "  phone: {phone}")?;
        }
        if let Some(stelic) = &self.statelic {
            writeln!(f, "  statelic: {stelic}")?;
        }
        writeln!(f)?;
        Ok(())
    }
}

impl AdvisorPartial {
    #[allow(clippy::result_large_err)]
    pub fn validate(self) -> Result<AdvisorOutput, AdvisorError> {
        let mut errors = AdvisorError {
            advisor_uuid: self.advisor_uuid,
            gbu_esid: None,
            first_name: None,
            last_name: None,
            address1: None,
            address2: None,
            city: None,
            state: None,
            zip: None,
            email: None,
            phone: None,
            statelic: None,
        };

        let gbu_esid = self.gbu_esid.clone().unwrap_or_default().trim().to_string();
        let first_name = self.first_name.trim().to_string();
        let last_name = self.last_name.trim().to_string();
        let address1 = self.address1.unwrap_or_default().trim().to_string();
        let address2 = self.address2.and_then(|a| {
            if a.trim().is_empty() {
                None
            } else {
                Some(a.trim().to_string())
            }
        });
        let city = self.city.unwrap_or_default().trim().to_string();
        let state = self.state.unwrap_or_default().trim().to_string();
        let zip = self.zip.unwrap_or_default().trim().to_string();
        let email = self.email.unwrap_or_default().trim().to_string();
        let phone = self.phone.unwrap_or_default().trim().to_string();
        let statelic = self.statelic;

        if gbu_esid.is_empty() {
            errors.gbu_esid = Some("GBU Advisor ID is required.".to_string());
        } else if !self.gbu_esid_valid {
            errors.gbu_esid = Some("Invalid or Inactive GBU Advisor ID.".to_string());
        }

        if first_name.is_empty() {
            errors.first_name = Some("First name is required.".to_string());
        }
        if last_name.is_empty() {
            errors.last_name = Some("Last name is required.".to_string());
        }
        if email.is_empty() {
            errors.email = Some("Email is required.".to_string());
        }
        if phone.is_empty() {
            errors.phone = Some("Phone is required.".to_string());
        }
        if address1.is_empty() {
            errors.address1 = Some("Address is required.".to_string());
        }
        if city.is_empty() {
            errors.city = Some("City is required.".to_string());
        }
        if state.is_empty() {
            errors.state = Some("State is required.".to_string());
        }
        if zip.is_empty() {
            errors.zip = Some("Zip is required.".to_string());
        }
        if statelic.is_empty() {
            errors.statelic = Some("State Licensing selection is required.".to_string());
        }

        if errors.gbu_esid.is_some()
            || errors.first_name.is_some()
            || errors.last_name.is_some()
            || errors.email.is_some()
            || errors.phone.is_some()
            || errors.address1.is_some()
            || errors.city.is_some()
            || errors.state.is_some()
            || errors.zip.is_some()
            || errors.email.is_some()
            || errors.phone.is_some()
            || errors.statelic.is_some()
        {
            return Err(errors);
        }

        Ok(AdvisorOutput {
            advisor_uuid: self.advisor_uuid,
            gbu_esid,
            first_name,
            last_name,
            address1,
            address2,
            city,
            state,
            zip,
            email,
            phone,
            statelic,
        })
    }
}

//-------------------------------------------------------------------------------------------------
pub struct Contact {
    pub first_name: String,
    pub last_name: String,
    pub email: String,
    pub phone: String,
    pub address1: String,
    pub address2: Option<String>,
    pub city: String,
    pub state: String,
    pub zip: String,
    pub gender: crate::types::Gender,
    pub birth_date: granite::DateUtc,
}

#[granite::gtype]
pub struct ContactInput {
    pub first_name: Option<String>,
    pub last_name: Option<String>,
    pub email: Option<String>,
    pub phone: Option<String>,
    pub address1: Option<String>,
    pub address2: Option<String>,
    pub city: Option<String>,
    pub state: Option<String>,
    pub zip: Option<String>,
    pub gender: Option<String>,
    pub birth_date: Option<DateUtc>,
}

#[granite::gtype]
pub struct ContactError {
    pub first_name: Option<String>,
    pub last_name: Option<String>,
    pub email: Option<String>,
    pub phone: Option<String>,
    pub address1: Option<String>,
    pub address2: Option<String>,
    pub city: Option<String>,
    pub state: Option<String>,
    pub zip: Option<String>,
    pub gender: Option<String>,
    pub birth_date: Option<String>,
}

impl std::fmt::Display for ContactError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        writeln!(f, "Contact Errors:")?;
        if let Some(first_name) = &self.first_name {
            writeln!(f, "  first_name: {first_name}")?;
        }
        if let Some(last_name) = &self.last_name {
            writeln!(f, "  last_name: {last_name}")?;
        }
        if let Some(email) = &self.email {
            writeln!(f, "  email: {email}")?;
        }
        if let Some(phone) = &self.phone {
            writeln!(f, "  phone: {phone}")?;
        }
        if let Some(address1) = &self.address1 {
            writeln!(f, "  address1: {address1}")?;
        }
        if let Some(address2) = &self.address2 {
            writeln!(f, "  address2: {address2}")?;
        }
        if let Some(city) = &self.city {
            writeln!(f, "  city: {city}")?;
        }
        if let Some(state) = &self.state {
            writeln!(f, "  state: {state}")?;
        }
        if let Some(zip) = &self.zip {
            writeln!(f, "  zip: {zip}")?;
        }
        if let Some(gender) = &self.gender {
            writeln!(f, "  gender: {gender}")?;
        }
        if let Some(birth_date) = &self.birth_date {
            writeln!(f, "  birth_date: {birth_date}")?;
        }
        writeln!(f)?;
        Ok(())
    }
}

impl ContactInput {
    #[allow(clippy::result_large_err)]
    pub fn validate(&self) -> Result<Contact, ContactError> {
        let mut errors = ContactError {
            first_name: None,
            last_name: None,
            email: None,
            phone: None,
            address1: None,
            address2: None,
            city: None,
            state: None,
            zip: None,
            gender: None,
            birth_date: None,
        };

        let first_name = self
            .first_name
            .clone()
            .unwrap_or_default()
            .trim()
            .to_string();
        let last_name = self
            .last_name
            .clone()
            .unwrap_or_default()
            .trim()
            .to_string();
        let email = self.email.clone().unwrap_or_default().trim().to_string();
        let phone = self.phone.clone().unwrap_or_default().trim().to_string();
        let address1 = self.address1.clone().unwrap_or_default().trim().to_string();
        let address2 = self.address2.clone().and_then(|a| {
            if a.trim().is_empty() {
                None
            } else {
                Some(a.trim().to_string())
            }
        });
        let city = self.city.clone().unwrap_or_default().trim().to_string();
        let state = self.state.clone().unwrap_or_default().trim().to_string();
        let zip = self.zip.clone().unwrap_or_default().trim().to_string();
        let gender = self.gender.clone().unwrap_or_default().trim().to_string();
        let birth_date = self.birth_date.unwrap_or_default();

        if first_name.is_empty() {
            errors.first_name = Some("First name is required.".to_string());
        }
        if last_name.is_empty() {
            errors.last_name = Some("Last name is required.".to_string());
        }

        if email.is_empty() {
            errors.email = Some("Email is required.".to_string());
        } else if !granite::validate_email(&email) {
            errors.email = Some("Invalid email address.".to_string());
        }

        if phone.is_empty() {
            errors.phone = Some("Phone is required.".to_string());
        } else if !granite::validate_phone_us(&phone) {
            errors.phone = Some("Invalid United States phone number.".to_string());
        }

        if address1.is_empty() {
            errors.address1 = Some("Address is required.".to_string());
        }

        if city.is_empty() {
            errors.city = Some("City is required.".to_string());
        }
        if state.is_empty() {
            errors.state = Some("State is required.".to_string());
        }
        if zip.is_empty() {
            errors.zip = Some("Zip is required.".to_string());
        }

        let gender = match gender.as_str() {
            "Male" => Some(crate::types::Gender::Male),
            "Female" => Some(crate::types::Gender::Female),
            _ => {
                errors.gender = Some("Gender is required.".to_string());
                None
            }
        };

        // make sure age_in_years is between 25 and 60
        if !(25..=60).contains(&granite::age_in_years_now(birth_date)) {
            errors.birth_date = Some("Applicant must be between 25 and 60 years old.".to_string());
        }

        if errors.first_name.is_some()
            || errors.last_name.is_some()
            || errors.email.is_some()
            || errors.phone.is_some()
            || errors.address1.is_some()
            || errors.city.is_some()
            || errors.state.is_some()
            || errors.zip.is_some()
            || errors.gender.is_some()
            || errors.birth_date.is_some()
        {
            return Err(errors);
        }

        Ok(Contact {
            first_name: first_name.to_string(),
            last_name,
            email,
            phone,
            address1,
            address2,
            city,
            state,
            zip,
            gender: gender.expect("cannot be none"),
            birth_date,
        })
    }
}

//-------------------------------------------------------------------------------------------------
/// Regarding `connected` bool:
/// - None means no active connection
/// - True means connection complete
/// - False means connection incomplete

#[granite::gtype(RsType, RsDebug)]
pub struct CrsInput {
    pub applicant_enabled: Option<bool>,
    pub applicant_verified: bool,
    pub spouse_enabled: Option<bool>,
    pub spouse_verified: bool,
}

pub struct Crs {
    pub applicant_active: bool,
    pub spouse_active: bool,
}

pub struct CrsError {
    pub applicant_error: Option<String>,
    pub spouse_error: Option<String>,
}
impl CrsInput {
    pub fn validate(self) -> Result<Crs, CrsError> {
        let applicant_result = match (self.applicant_enabled, self.applicant_verified) {
            // They enabled it and have verified.
            (Some(true), true) => Ok(true),

            // They have enabled it but not verified.
            (Some(true), false) => Err(
                "You must either complete the Primary Applicant soft credit pull or disable it."
                    .to_string(),
            ),

            // They have disabled it.  No errors.
            (Some(false), _) => Ok(false),

            // They haven't made a decision.
            (None, _) => Err(
                "You must either complete the Primary Applicant soft credit pull or disable it."
                    .to_string(),
            ),
        };

        let spouse_result = match (self.spouse_enabled, self.spouse_verified) {
            // They enabled it and have verified.
            (Some(true), true) => Ok(true),

            // They have enabled it but not verified.
            (Some(true), false) => Err(
                "You must either complete the Spouse soft credit pull or disable it.".to_string(),
            ),

            // They have disabled it.  No errors.
            (Some(false), _) => Ok(false),

            // They haven't made a decision.
            (None, _) => Err(
                "You must either complete the Spouse soft credit pull or disable it.".to_string(),
            ),
        };

        // Ok if both are Ok, Err if either is Err
        if let Ok(applicant) = applicant_result
            && let Ok(spouse) = spouse_result
        {
            Ok(Crs {
                applicant_active: applicant,
                spouse_active: spouse,
            })
        } else {
            Err(CrsError {
                applicant_error: applicant_result.err(),
                spouse_error: spouse_result.err(),
            })
        }
    }
}

//-------------------------------------------------------------------------------------------------

use crate::debt::DebtRow;
use crate::debt::Debts;
use crate::debt::DebtsError;
use crate::policy::{Pua, PuaError, PuaInput};

pub fn validate_debts_input(debts: &[DebtRow]) -> Result<Debts, DebtsError> {
    if debts.is_empty() {
        return Err(DebtsError {
            outer: "Must have at least one debt.".to_string(),
            inner: Vec::new(),
        });
    }

    // delegate work to crate
    crate::debt::Debts::new(granite::current_date(), debts)
}

//-------------------------------------------------------------------------------------------------
#[granite::gtype(ApiOutput)]
pub struct BudgetOutput {
    pub budget_extra_debts: Decimal,
    pub budget_extra_savings: Decimal,
    pub budget_extra_retirement: Decimal,
    pub budget_extra_surplus: Decimal,
    pub budget_pua_split: String,
    pub budget_pua_contribution: Decimal,
}

pub struct BudgetInput {
    pub budget_extra_debts: Option<Decimal>,
    pub budget_extra_savings: Option<Decimal>,
    pub budget_extra_retirement: Option<Decimal>,
    pub budget_extra_surplus: Option<Decimal>,
    pub budget_pua_split: Option<String>,
    pub budget_pua_contribution: Option<Decimal>,
}

#[granite::gtype(ApiOutput)]
pub struct BudgetError {
    pub message: String,
    pub budget_extra_debts: Option<String>,
    pub budget_extra_savings: Option<String>,
    pub budget_extra_retirement: Option<String>,
    pub budget_extra_surplus: Option<String>,
    pub budget_pua_split: Option<String>,
    pub budget_pua_contribution: Option<String>,
}

impl BudgetInput {
    #[allow(clippy::result_large_err)]
    pub fn validate(&self) -> Result<BudgetOutput, BudgetError> {
        let mut error = BudgetError {
            message:
                "There are some errors in your input.  Please correct the errors and try again."
                    .to_string(),
            budget_extra_debts: None,
            budget_extra_savings: None,
            budget_extra_retirement: None,
            budget_extra_surplus: None,
            budget_pua_split: None,
            budget_pua_contribution: None,
        };

        match self.budget_extra_debts {
            Some(d) => {
                if d.lt(&Decimal::ZERO) {
                    error.budget_extra_debts = Some("Must be positive.".to_string());
                }
                if d.gt(&Decimal::new(1000000000, 0)) {
                    error.budget_extra_debts =
                        Some("Must be less than $1,000,000,000.".to_string());
                }
            }
            None => {
                error.budget_extra_debts = Some("Input is required (it can be $0).".to_string());
            }
        }

        match self.budget_extra_savings {
            Some(d) => {
                if d.lt(&Decimal::ZERO) {
                    error.budget_extra_savings = Some("Must be positive.".to_string());
                }
                if d.gt(&Decimal::new(1000000000, 0)) {
                    error.budget_extra_savings =
                        Some("Must be less than $1,000,000,000.".to_string());
                }
            }
            None => {
                error.budget_extra_savings = Some("Input is required (it can be $0).".to_string());
            }
        }

        match self.budget_extra_retirement {
            Some(d) => {
                if d.lt(&Decimal::ZERO) {
                    error.budget_extra_retirement = Some("Must be positive.".to_string());
                }
                if d.gt(&Decimal::new(1000000000, 0)) {
                    error.budget_extra_retirement =
                        Some("Must be less than $1,000,000,000.".to_string());
                }
            }
            None => {
                error.budget_extra_retirement =
                    Some("Input is required (it can be $0).".to_string());
            }
        }

        match self.budget_extra_surplus {
            Some(d) => {
                if d.lt(&Decimal::ZERO) {
                    error.budget_extra_surplus = Some("Must be positive.".to_string());
                }
                if d.gt(&Decimal::new(1000000000, 0)) {
                    error.budget_extra_surplus =
                        Some("Must be less than $1,000,000,000.".to_string());
                }
            }
            None => {
                error.budget_extra_surplus = Some("Input is required (it can be $0).".to_string());
            }
        }

        match self.budget_pua_contribution {
            Some(d) => {
                if d.lt(&Decimal::ZERO) {
                    error.budget_pua_contribution = Some("Must be positive.".to_string());
                }
                if d.gt(&Decimal::new(1000000000, 0)) {
                    error.budget_pua_contribution =
                        Some("Must be less than $1,000,000,000.".to_string());
                }
            }
            None => {
                error.budget_pua_contribution =
                    Some("Input is required (it can be $0).".to_string());
            }
        }

        if error.budget_extra_debts.is_some()
            || error.budget_extra_savings.is_some()
            || error.budget_extra_retirement.is_some()
            || error.budget_extra_surplus.is_some()
            || error.budget_pua_split.is_some()
            || error.budget_pua_contribution.is_some()
        {
            return Err(error);
        }

        Ok(BudgetOutput {
            budget_extra_debts: self.budget_extra_debts.unwrap_or_default(),
            budget_extra_savings: self.budget_extra_savings.unwrap_or_default(),
            budget_extra_retirement: self.budget_extra_retirement.unwrap_or_default(),
            budget_extra_surplus: self.budget_extra_surplus.unwrap_or_default(),
            budget_pua_split: self.budget_pua_split.clone().unwrap_or_default(),
            budget_pua_contribution: self.budget_pua_contribution.unwrap_or_default(),
        })
    }
}

//-------------------------------------------------------------------------------------------------

pub struct ICoverOutput {
    pub icover_url: String,
}

#[granite::gtype(ApiOutput)]
pub struct ICoverErrorMessage {
    pub message: String,
    pub url: Option<String>,
}
