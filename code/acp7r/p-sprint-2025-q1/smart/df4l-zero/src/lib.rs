pub mod api;
pub mod client;
pub mod debt;
pub mod gbu_agent;
pub mod plan;
pub mod policy;
pub mod types;
pub mod web;

pub trait App: approck::App + approck_postgres::App + api_crs::App {
    fn icover_client_login_url(&self, client_uuid: granite::Uuid) -> String;
}

#[allow(async_fn_in_trait)]
pub trait Identity: approck::Identity + auth_fence::Identity {
    fn web_usage(&self) -> bool {
        true
    }
    fn api_usage(&self) -> bool {
        true
    }

    async fn df4l_zero_client_read(
        &self,
        dbcx: &impl approck_postgres::DB,
        client_uuid: granite::Uuid,
    ) -> bool;
}

pub trait Document: bux::document::Base {}

pub fn ml_advisor_dashboard_client_details(
    advisor_uuid: granite::Uuid,
    client_uuid: granite::Uuid,
) -> String {
    format!("/advisor/{advisor_uuid}/client0/{client_uuid}/")
}

pub fn ml_myaccount_advisor_gbu(advisor_uuid: granite::Uuid) -> String {
    format!("/myaccount/advisor/{advisor_uuid}/gbu")
}

pub fn ml_myaccount_advisor_statelic(advisor_uuid: granite::Uuid) -> String {
    format!("/myaccount/advisor/{advisor_uuid}/statelic")
}

pub fn ml_myaccount_advisor_email(advisor_uuid: granite::Uuid) -> String {
    format!("/myaccount/advisor/{advisor_uuid}/email")
}

pub fn ml_myaccount_advisor_address(advisor_uuid: granite::Uuid) -> String {
    format!("/myaccount/advisor/{advisor_uuid}/address")
}

pub fn ml_myaccount_advisor_phone(advisor_uuid: granite::Uuid) -> String {
    format!("/myaccount/advisor/{advisor_uuid}/phone")
}

pub fn ml_advisor(advisor_uuid: granite::Uuid) -> String {
    format!("/advisor/{advisor_uuid}/")
}

pub fn ml_advisor_client_list(advisor_uuid: granite::Uuid) -> String {
    format!("/advisor/{advisor_uuid}/client/")
}

pub fn ml_advisor_client_pending(advisor_uuid: granite::Uuid) -> String {
    format!("/advisor/{advisor_uuid}/client/?pending=true")
}

pub fn ml_advisor_client_active(advisor_uuid: granite::Uuid) -> String {
    format!("/advisor/{advisor_uuid}/client/?pending=false")
}

pub fn ml_advisor_client_add(advisor_uuid: granite::Uuid) -> String {
    format!("/advisor/{advisor_uuid}/client/add")
}

pub fn ml_advisor_client_details(
    advisor_uuid: granite::Uuid,
    client_uuid: granite::Uuid,
) -> String {
    format!("/advisor/{advisor_uuid}/client/{client_uuid}/")
}

pub fn ml_advisor_client_wizard_contact(
    advisor_uuid: granite::Uuid,
    client_uuid: granite::Uuid,
) -> String {
    format!("/advisor/{advisor_uuid}/client/{client_uuid}/wizard/contact")
}

pub fn ml_advisor_client_wizard(advisor_uuid: granite::Uuid, client_uuid: granite::Uuid) -> String {
    format!("/advisor/{advisor_uuid}/client/{client_uuid}/wizard/")
}

pub fn ml_advisor_client_wizard_crs(
    advisor_uuid: granite::Uuid,
    client_uuid: granite::Uuid,
) -> String {
    format!("/advisor/{advisor_uuid}/client/{client_uuid}/wizard/crs")
}

pub fn ml_advisor_client_wizard_debt(
    advisor_uuid: granite::Uuid,
    client_uuid: granite::Uuid,
) -> String {
    format!("/advisor/{advisor_uuid}/client/{client_uuid}/wizard/debt")
}

pub fn ml_advisor_client_wizard_budget(
    advisor_uuid: granite::Uuid,
    client_uuid: granite::Uuid,
) -> String {
    format!("/advisor/{advisor_uuid}/client/{client_uuid}/wizard/budget")
}

pub fn ml_advisor_client_wizard_policy(
    advisor_uuid: granite::Uuid,
    client_uuid: granite::Uuid,
) -> String {
    format!("/advisor/{advisor_uuid}/client/{client_uuid}/wizard/policy")
}

pub fn ml_advisor_client_wizard_icover(
    advisor_uuid: granite::Uuid,
    client_uuid: granite::Uuid,
) -> String {
    format!("/advisor/{advisor_uuid}/client/{client_uuid}/wizard/icover")
}

pub fn ml_advisor_client_wizard_report(
    advisor_uuid: granite::Uuid,
    client_uuid: granite::Uuid,
) -> String {
    format!("/advisor/{advisor_uuid}/client/{client_uuid}/wizard/report")
}

pub fn ml_advisor_client_messages(
    advisor_uuid: granite::Uuid,
    client_uuid: granite::Uuid,
) -> String {
    format!("/advisor/{advisor_uuid}/client/{client_uuid}/messages")
}

pub fn ml_advisor_client0_list(advisor_uuid: granite::Uuid) -> String {
    format!("/advisor/{advisor_uuid}/client0/")
}

pub fn ml_advisor_client0_add(advisor_uuid: granite::Uuid) -> String {
    format!("/advisor/{advisor_uuid}/client0/add")
}

pub fn ml_advisor_client0_details(
    advisor_uuid: granite::Uuid,
    client_uuid: granite::Uuid,
) -> String {
    format!("/advisor/{advisor_uuid}/client0/{client_uuid}/")
}

pub fn ml_advisor_client0_edit(advisor_uuid: granite::Uuid, client_uuid: granite::Uuid) -> String {
    format!("/advisor/{advisor_uuid}/client0/{client_uuid}/edit")
}
pub fn ml_advisor_client0_lfe_insurance(
    advisor_uuid: granite::Uuid,
    client_uuid: granite::Uuid,
) -> String {
    format!("/advisor/{advisor_uuid}/client0/{client_uuid}/lfe-insurance/")
}

pub fn ml_advisor_client0_debt_add(
    advisor_uuid: granite::Uuid,
    client_uuid: granite::Uuid,
) -> String {
    format!("/advisor/{advisor_uuid}/client0/{client_uuid}/debt/add")
}

pub fn ml_advisor_client0_debt_edit(
    advisor_uuid: granite::Uuid,
    client_uuid: granite::Uuid,
    client_debt_uuid: granite::Uuid,
) -> String {
    format!("/advisor/{advisor_uuid}/client0/{client_uuid}/debt/{client_debt_uuid}/edit")
}

pub fn ml_advisor_client0_debt_delete(
    advisor_uuid: granite::Uuid,
    client_uuid: granite::Uuid,
    client_debt_uuid: granite::Uuid,
) -> String {
    format!("/advisor/{advisor_uuid}/client0/{client_uuid}/debt/{client_debt_uuid}/delete")
}

pub fn ml_advisor_client0_debt_details(
    advisor_uuid: granite::Uuid,
    client_uuid: granite::Uuid,
    client_debt_uuid: granite::Uuid,
) -> String {
    format!("/advisor/{advisor_uuid}/client0/{client_uuid}/debt/{client_debt_uuid}/")
}
pub fn ml_advisor_client0_df4l_edit(
    advisor_uuid: granite::Uuid,
    client_uuid: granite::Uuid,
) -> String {
    format!("/advisor/{advisor_uuid}/client0/{client_uuid}/df4l/edit")
}

pub fn ml_advisor_client0_debt_list(
    advisor_uuid: granite::Uuid,
    client_uuid: granite::Uuid,
) -> String {
    format!("/advisor/{advisor_uuid}/client0/{client_uuid}/debt/")
}

pub fn ml_advisor_client0_note_list(
    advisor_uuid: granite::Uuid,
    client_uuid: granite::Uuid,
) -> String {
    format!("/advisor/{advisor_uuid}/client0/{client_uuid}/note/")
}

pub fn ml_advisor_client0_note_add(
    advisor_uuid: granite::Uuid,
    client_uuid: granite::Uuid,
) -> String {
    format!("/advisor/{advisor_uuid}/client0/{client_uuid}/note/add")
}

pub fn ml_advisor_client0_note_view(
    advisor_uuid: granite::Uuid,
    client_uuid: granite::Uuid,
    client_note_uuid: granite::Uuid,
) -> String {
    format!("/advisor/{advisor_uuid}/client0/{client_uuid}/note/{client_note_uuid}/")
}

pub fn ml_advisor_client0_life_insurance(
    advisor_uuid: granite::Uuid,
    client_uuid: granite::Uuid,
) -> String {
    format!("/advisor/{advisor_uuid}/client0/{client_uuid}/life-insurance/")
}

pub fn ml_advisor_client0_df4l_dump(
    advisor_uuid: granite::Uuid,
    client_uuid: granite::Uuid,
) -> String {
    format!("/advisor/{advisor_uuid}/client0/{client_uuid}/df4l/dump")
}

pub fn ml_advisor_client0_df4l_report(
    advisor_uuid: granite::Uuid,
    client_uuid: granite::Uuid,
) -> String {
    format!("/advisor/{advisor_uuid}/client0/{client_uuid}/df4l/report")
}

pub fn ml_advisor_client0_df4l_activate(
    advisor_uuid: granite::Uuid,
    client_uuid: granite::Uuid,
) -> String {
    format!("/advisor/{advisor_uuid}/client0/{client_uuid}/df4l/activate")
}

// NOTE: new client ml functions
pub fn ml_advisor_client_debt_list(
    advisor_uuid: granite::Uuid,
    client_uuid: granite::Uuid,
) -> String {
    format!("/advisor/{advisor_uuid}/client/{client_uuid}/debt/")
}

pub fn ml_advisor_client_debt_add(
    advisor_uuid: granite::Uuid,
    client_uuid: granite::Uuid,
) -> String {
    format!("/advisor/{advisor_uuid}/client/{client_uuid}/debt/add")
}

pub fn ml_advisor_client_debt_edit(
    advisor_uuid: granite::Uuid,
    client_uuid: granite::Uuid,
    client_debt_uuid: granite::Uuid,
) -> String {
    format!("/advisor/{advisor_uuid}/client/{client_uuid}/debt/{client_debt_uuid}/edit")
}

pub fn ml_advisor_client_debt_details(
    advisor_uuid: granite::Uuid,
    client_uuid: granite::Uuid,
    client_debt_uuid: granite::Uuid,
) -> String {
    format!("/advisor/{advisor_uuid}/client/{client_uuid}/debt/{client_debt_uuid}/")
}

pub fn ml_advisor_client_debt_delete(
    advisor_uuid: granite::Uuid,
    client_uuid: granite::Uuid,
    client_debt_uuid: granite::Uuid,
) -> String {
    format!("/advisor/{advisor_uuid}/client/{client_uuid}/debt/{client_debt_uuid}/delete")
}
