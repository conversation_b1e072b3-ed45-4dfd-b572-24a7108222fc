use postgres_types::{FromSql, ToSql};

#[derive(ToSql, FromSql, Clone, PartialEq)]
#[postgres(name = "client_type")]
#[granite::gtype(ApiInput, ApiOutput)]
pub enum ClientType {
    DebtManagement,
    PolicyOnly,
}

#[granite::gtype(ApiInput)]
#[derive(Co<PERSON>, <PERSON>lone)]
pub enum CreditReportSubject {
    Applicant,
    Spouse,
}

impl std::fmt::Display for CreditReportSubject {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            CreditReportSubject::Applicant => write!(f, "Applicant"),
            CreditReportSubject::Spouse => write!(f, "Spouse"),
        }
    }
}

#[derive(Co<PERSON>, Clone, PartialEq)]
#[granite::gtype(RsType, RsPartial, RsError, RsDebug)]
pub enum Gender {
    Male,
    Female,
}
impl std::fmt::Display for Gender {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            Gender::Male => write!(f, "Male"),
            Gender::Female => write!(f, "Female"),
        }
    }
}
