use api_crs::types::CrsUserData;
use granite::return_invalid_data;

impl api_crs::App for crate::AppStruct {
    fn api_crs_module(&self) -> &api_crs::ModuleStruct {
        &self.api_crs
    }

    // look up data from postgres living in df4l.client
    async fn get_crs_user_data(
        &self,
        dbcx: &impl approck_postgres::DB,
        api_crs_user_uuid: granite::Uuid,
    ) -> granite::Result<CrsUserData> {
        // Since the df4l.client is the only table that references api_crs_user_uuid, we can use it to determine if the user is an applicant or spouse
        let row = granite::pg_row!(
            db = dbcx;
            args = {
                $api_crs_user_uuid: &api_crs_user_uuid,
            };
            row = {
                api_crs_user_uuid_applicant: Option<Uuid>,
                api_crs_user_uuid_spouse: Option<Uuid>,
                first_name: String,
                last_name: String,
                email: Option<String>,
                phone: Option<String>,
                gender: Option<String>,
                birth_date: Option<DateUtc>,
                address1: Option<String>,
                address2: Option<String>,
                city: Option<String>,
                state: Option<String>,
                zip: Option<String>,
            };
            SELECT
                api_crs_user_uuid_applicant,
                api_crs_user_uuid_spouse,
                first_name,
                last_name,
                email,
                phone,
                gender,
                birth_date,
                address1,
                address2,
                city,
                state,
                zip
            FROM
                df4l.client
            WHERE
                (
                    api_crs_user_uuid_applicant = $api_crs_user_uuid::uuid
                    OR
                    api_crs_user_uuid_spouse = $api_crs_user_uuid::uuid
                )

        )
        .await?;

        match (
            row.api_crs_user_uuid_applicant,
            row.api_crs_user_uuid_spouse,
        ) {
            // Primary Applicant uses all available data
            (Some(uuid), _) if uuid == api_crs_user_uuid => Ok(CrsUserData {
                api_crs_user_uuid,
                first_name: Some(row.first_name),
                last_name: Some(row.last_name),
                email: row.email,
                phone: row.phone,
                gender: row.gender,
                birth_date: row.birth_date,
                address1: row.address1,
                address2: row.address2,
                city: row.city,
                state: row.state,
                zip: row.zip,
            }),
            // Spouse: keep last name, address
            (_, Some(uuid)) if uuid == api_crs_user_uuid => Ok(CrsUserData {
                api_crs_user_uuid,
                first_name: None,
                last_name: Some(row.last_name),
                email: None,
                phone: None,
                gender: None,
                birth_date: None,
                address1: row.address1,
                address2: row.address2,
                city: row.city,
                state: row.state,
                zip: row.zip,
            }),
            (a, b) => return_invalid_data!("Logically impossible state: {:?}, {:?}", a, b),
        }
    }
}

impl api_crs::Identity for crate::IdentityStruct {
    async fn user_read(
        &self,
        dbcx: &impl approck_postgres::DB,
        api_crs_user_uuid: granite::Uuid,
    ) -> bool {
        if self.df4l_admin.is_some() {
            return true;
        }

        // If logged in as one or more advisors, then check if any of their clients match
        // a primary applicant or spouse using the passed api_crs_user_uuid
        if let Some(df4l_advisor) = &self.df4l_advisor {
            let advisor_uidl: Vec<granite::Uuid> = df4l_advisor.keys().cloned().collect();

            // query to see if record exists with this advisor and api_crs_user_uuid
            let row = granite::pg_row!(
                db = dbcx;
                args = {
                    $api_crs_user_uuid: &api_crs_user_uuid,
                    $advisor_uidl: &advisor_uidl,
                };
                row = {
                    exists: bool,
                };
                SELECT
                    EXISTS (
                        SELECT
                        FROM df4l.client
                        WHERE true
                            AND advisor_uuid = ANY($advisor_uidl::uuid[])
                            AND (
                                api_crs_user_uuid_applicant = $api_crs_user_uuid::uuid
                                OR
                                api_crs_user_uuid_spouse = $api_crs_user_uuid::uuid
                            )
                    ) AS exists
            )
            .await;

            match row {
                // Return true if matches
                Ok(row) if row.exists => return true,

                // Otherwise fall through
                Ok(_) => (),

                // Report error and fall through
                Err(e) => {
                    approck::error!("Error checking user_read permission: {:?}", e);
                }
            }
        }

        false
    }

    async fn user_write(
        &self,
        dbcx: &impl approck_postgres::DB,
        api_crs_user_uuid: granite::Uuid,
    ) -> bool {
        // delegate to user_read
        self.user_read(dbcx, api_crs_user_uuid).await
    }
}
