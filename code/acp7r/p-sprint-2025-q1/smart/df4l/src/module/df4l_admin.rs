use crate::{AppStruct, IdentityStruct};

impl df4l_admin::App for AppStruct {}

impl df4l_admin::Identity for IdentityStruct {
    /// Allow access if Admin
    fn web_usage(&self) -> bool {
        if self.df4l_admin.is_some() {
            return true;
        }

        false
    }

    // this follows web usage
    fn api_usage(&self) -> bool {
        self.web_usage()
    }

    fn agency_list(&self) -> bool {
        match &self.df4l_admin {
            Some(df4l_admin) => df4l_admin.agency_read,
            None => false,
        }
    }
    fn agency_add(&self) -> bool {
        match &self.df4l_admin {
            Some(df4l_admin) => df4l_admin.agency_write,
            None => false,
        }
    }
    fn agency_read(&self, _agency_uuid: granite::Uuid) -> bool {
        match &self.df4l_admin {
            Some(df4l_admin) => df4l_admin.agency_read,
            None => false,
        }
    }
    fn agency_write(&self, _agency_uuid: granite::Uuid) -> bool {
        match &self.df4l_admin {
            Some(df4l_admin) => df4l_admin.agency_write,
            None => false,
        }
    }
    fn agency_advisor_list(&self, _agency_uuid: granite::Uuid) -> bool {
        match &self.df4l_admin {
            Some(df4l_admin) => df4l_admin.agency_read,
            None => false,
        }
    }
    fn agency_client_list(&self, _agency_uuid: granite::Uuid) -> bool {
        match &self.df4l_admin {
            Some(df4l_admin) => df4l_admin.agency_read,
            None => false,
        }
    }

    fn advisor_list(&self) -> bool {
        match &self.df4l_admin {
            Some(df4l_admin) => df4l_admin.advisor_read,
            None => false,
        }
    }
    fn advisor_add(&self) -> bool {
        match &self.df4l_admin {
            Some(df4l_admin) => df4l_admin.advisor_write,
            None => false,
        }
    }
    fn advisor_read(&self, _advisor_uuid: granite::Uuid) -> bool {
        match &self.df4l_admin {
            Some(df4l_admin) => df4l_admin.advisor_read,
            None => false,
        }
    }
    fn advisor_write(&self, _advisor_uuid: granite::Uuid) -> bool {
        match &self.df4l_admin {
            Some(df4l_admin) => df4l_admin.advisor_write,
            None => false,
        }
    }

    fn client_list(&self) -> bool {
        match &self.df4l_admin {
            Some(df4l_admin) => df4l_admin.client_read,
            None => false,
        }
    }
    fn client_add(&self) -> bool {
        match &self.df4l_admin {
            Some(df4l_admin) => df4l_admin.client_write,
            None => false,
        }
    }
    fn client_write(&self, _client_uuid: granite::Uuid) -> bool {
        match &self.df4l_admin {
            Some(df4l_admin) => df4l_admin.client_write,
            None => false,
        }
    }
    fn client_read(&self, _client_uuid: granite::Uuid) -> bool {
        match &self.df4l_admin {
            Some(df4l_admin) => df4l_admin.client_read,
            None => false,
        }
    }
}
