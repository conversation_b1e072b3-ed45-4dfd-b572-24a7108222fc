impl df4l_public::App for crate::AppStruct {}

impl df4l_public::Identity for crate::IdentityStruct {
    async fn signup_advisor_read(
        &self,
        dbcx: &impl approck_postgres::DB,
        signup_advisor_uuid: granite::Uuid,
    ) -> bool {
        self.signup_advisor_write(dbcx, signup_advisor_uuid).await
    }

    async fn signup_advisor_write(
        &self,
        dbcx: &impl approck_postgres::DB,
        signup_advisor_uuid: granite::Uuid,
    ) -> bool {
        let session_token = &self.request.session_token;

        // if the session token matches the signup_advisor_uuid, allow access
        let result = granite::pg_row_option!(
            db = dbcx;
            args = {
                $session_token: &session_token,
                $signup_advisor_uuid: &signup_advisor_uuid,
            };
            row = {
                exists: bool,
            };
            SELECT
                true as exists
            FROM
                df4l.signup_advisor
            WHERE
                signup_advisor_uuid = $signup_advisor_uuid::uuid
                AND session_token = $session_token::varchar
        )
        .await;

        match result {
            Ok(Some(_row)) => true,
            Ok(None) => false,
            Err(e) => {
                approck::error!("Error checking signup_advisor_read permission: {:?}", e);
                false
            }
        }
    }

    fn remote_addr(&self) -> std::net::IpAddr {
        self.request.remote_address
    }
}
