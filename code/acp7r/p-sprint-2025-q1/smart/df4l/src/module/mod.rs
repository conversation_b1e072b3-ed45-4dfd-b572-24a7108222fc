mod addr_iso;
mod api_crs;
mod api_sendgrid;
mod api_sentry;
mod api_stripe;
mod api_twilio;
mod approck_postgres;
mod approck_redis;
mod approck_server;
pub(crate) mod auth_fence;
mod auth_fence_provider;
mod bux;
mod df4l_admin;
mod df4l_advisor;
mod df4l_gbu;
mod df4l_icover;
mod df4l_public;
mod df4l_zero;
mod legal_plane;
mod msg_io;
mod msg_io_sms;

impl crate::App for crate::AppStruct {}

impl crate::Identity for crate::IdentityStruct {
    fn web_usage(&self) -> bool {
        ::auth_fence::Identity::is_logged_in(self)
    }
    fn api_usage(&self) -> bool {
        ::auth_fence::Identity::is_logged_in(self)
    }
}

impl crate::Document for crate::DocumentStruct {}
