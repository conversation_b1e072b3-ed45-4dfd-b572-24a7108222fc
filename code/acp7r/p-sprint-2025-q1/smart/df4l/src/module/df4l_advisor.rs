use crate::{AppStruct, IdentityStruct};

impl df4l_advisor::App for AppStruct {}

impl df4l_advisor::Identity for IdentityStruct {
    /// Allow access if Admin or if Advisor
    fn web_usage(&self) -> bool {
        if self.df4l_admin.is_some() {
            return true;
        }

        if self.df4l_advisor.is_some() {
            return true;
        }

        false
    }

    // this follows web usage
    fn api_usage(&self) -> bool {
        self.web_usage()
    }

    fn advisor_read(&self, advisor_uuid: granite::Uuid) -> bool {
        if self.df4l_admin.is_some() {
            return true;
        }

        if let Some(df4l_advisor) = &self.df4l_advisor {
            if df4l_advisor.contains_key(&advisor_uuid) {
                return true;
            }
        }

        false
    }

    fn advisor_write(&self, advisor_uuid: granite::Uuid) -> bool {
        if self.df4l_admin.is_some() {
            return true;
        }

        if let Some(df4l_advisor) = &self.df4l_advisor {
            if df4l_advisor.contains_key(&advisor_uuid) {
                return true;
            }
        }

        false
    }

    fn client_list(&self, advisor_uuid: granite::Uuid) -> bool {
        if self.df4l_admin.is_some() {
            return true;
        }

        if let Some(df4l_advisor) = &self.df4l_advisor {
            if df4l_advisor.contains_key(&advisor_uuid) {
                return true;
            }
        }

        false
    }

    fn client_add(&self, advisor_uuid: granite::Uuid) -> bool {
        if self.df4l_admin.is_some() {
            return true;
        }

        if let Some(df4l_advisor) = &self.df4l_advisor {
            if df4l_advisor.contains_key(&advisor_uuid) {
                return true;
            }
        }

        false
    }

    async fn client_write(
        &self,
        dbcx: &impl approck_postgres::DB,
        client_uuid: granite::Uuid,
    ) -> bool {
        if self.df4l_admin.is_some() {
            return true;
        }

        // translate client to advisor for permission check
        let advisor_uuid = match client_uuid_to_advisor_uuid(dbcx, &client_uuid).await {
            Ok(advisor_uuid) => advisor_uuid,
            Err(e) => {
                approck::error!("Error looking up advisor_uuid for client_uuid: {:?}", e);
                return false;
            }
        };

        // check specific advisor's write permission
        if let Some(df4l_advisor) = &self.df4l_advisor {
            if let Some(df4l_advisor) = df4l_advisor.get(&advisor_uuid) {
                return df4l_advisor.client_write;
            }
        }

        false
    }

    async fn client_read(
        &self,
        dbcx: &impl approck_postgres::DB,
        client_uuid: granite::Uuid,
    ) -> bool {
        if self.df4l_admin.is_some() {
            return true;
        }

        // translate client to advisor for permission check
        let advisor_uuid = match client_uuid_to_advisor_uuid(dbcx, &client_uuid).await {
            Ok(advisor_uuid) => advisor_uuid,
            Err(e) => {
                approck::error!("Error looking up advisor_uuid for client_uuid: {:?}", e);
                return false;
            }
        };

        // check specific advisor's read permission
        if let Some(df4l_advisor) = &self.df4l_advisor {
            if let Some(df4l_advisor) = df4l_advisor.get(&advisor_uuid) {
                return df4l_advisor.client_read;
            }
        }

        false
    }
}

pub async fn client_uuid_to_advisor_uuid(
    dbcx: &impl approck_postgres::DB,
    client_uuid: &granite::Uuid,
) -> granite::Result<granite::Uuid> {
    // Check both tables and return the advisor_uuid
    let row = granite::pg_row!(
        db = dbcx;
        args = {
            $client_uuid: &client_uuid,
        };
        row = {
            advisor_uuid: Uuid,
        };

        // Look in client0
        SELECT
            advisor_uuid
        FROM
            df4l.client0
        WHERE true
            AND client_uuid = $client_uuid::uuid

        UNION

        // Look in client
        SELECT
            advisor_uuid
        FROM
            df4l.client
        WHERE true
            AND client_uuid = $client_uuid::uuid
    )
    .await?;

    Ok(row.advisor_uuid)
}
