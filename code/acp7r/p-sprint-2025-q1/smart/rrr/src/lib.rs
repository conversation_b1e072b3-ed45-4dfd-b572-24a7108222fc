#[path = "libλ.rs"]
pub mod libλ;

pub mod api;
pub mod module;
pub mod web;

///////////////////////////////////////////////////////////////////////////////////////////////////

///////////////////////////////////////////////////////////////////////////////////////////////////
pub trait App: approck_postgres::App + auth_fence::App {}

pub trait Identity: approck::Identity + auth_fence::Identity {
    fn web_usage(&self) -> bool;
    fn api_usage(&self) -> bool;
}

pub trait Document: bux::document::<PERSON><PERSON> {}
pub trait DocumentRRR: bux::document::Base {}

///////////////////////////////////////////////////////////////////////////////////////////////////

#[derive(serde::Deserialize)]
pub struct AppConfig {
    pub redis: approck_redis::ModuleConfig,
    pub postgres: approck_postgres::ModuleConfig,
    pub webserver: approck::server::ModuleConfig,
    pub auth_fence: auth_fence::types::ModuleConfig,
    pub api_twilio: api_twilio::ModuleConfig,
    pub api_sendgrid: api_sendgrid::ModuleConfig,
    pub api_sentry: api_sentry::ModuleConfig,
}

pub struct AppStruct {
    pub redis: approck_redis::ModuleStruct,
    pub postgres: approck_postgres::ModuleStruct,
    pub webserver: approck::server::Module,
    pub auth_fence: auth_fence::types::ModuleStruct,
    pub api_twilio: api_twilio::ModuleStruct,
    pub api_sendgrid: api_sendgrid::ModuleStruct,
    pub api_sentry: api_sentry::ModuleStruct,
    pub uuid_cache: approck::UuidCache,
}

#[allow(dead_code)]
#[derive(Debug)]
pub struct IdentityStruct {
    remote_address: std::net::IpAddr,
    session_token: String,
    auth_fence: Option<auth_fence::api::identity::Identity>,
    rrr_admin: Option<RrrAdminIdentity>,
    rrr_agent: Option<HashMap<granite::Uuid, RrrAgentIdentity>>,
}

#[derive(Debug)]
pub struct RrrAdminIdentity {
    agent_read: bool,
    agent_write: bool,
}

#[derive(Debug)]
pub struct RrrAgentIdentity {}

use std::collections::HashMap;

pub use crate::web::Document::Document as DocumentStruct;
pub use crate::web::DocumentRRR::DocumentRRR as DocumentRRRStruct;

///////////////////////////////////////////////////////////////////////////////////////////////////

impl approck::App for AppStruct {
    type Config = AppConfig;
    type Identity = IdentityStruct;
    fn new(config: Self::Config) -> granite::Result<Self> {
        use approck::Module;
        Ok(Self {
            redis: approck_redis::ModuleStruct::new(config.redis)?,
            postgres: approck_postgres::ModuleStruct::new(config.postgres)?,
            webserver: approck::server::Module::new(config.webserver)?,
            auth_fence: auth_fence::types::ModuleStruct::new(config.auth_fence)?,
            api_twilio: api_twilio::ModuleStruct::new(config.api_twilio)?,
            api_sendgrid: api_sendgrid::ModuleStruct::new(config.api_sendgrid)?,
            api_sentry: api_sentry::ModuleStruct::new(config.api_sentry)?,
            uuid_cache: approck::UuidCache::default(),
        })
    }
    async fn init(&self) -> granite::Result<()> {
        use approck::Module;
        self.redis.init().await?;
        self.postgres.init().await?;
        self.api_twilio.init().await?;
        self.api_sendgrid.init().await?;
        self.api_sentry.init().await?;
        self.auth_fence.init().await?;
        self.webserver.init().await?;
        // get the crate name using the env! macro
        let crate_name = env!("CARGO_PKG_NAME");
        println!("init: {crate_name}");

        // load uuid cache
        let dbcx = approck_postgres::App::postgres_dbcx(self).await?;
        let rows = granite::pg_row_vec!(
            db = dbcx;
            args = {};
            row = {
                uuid: Uuid,
                label: String,
            };
            SELECT
                agent_uuid AS uuid,
                first_name || " " || last_name AS label
            FROM
                rrr.agent
        )
        .await?;

        for row in rows {
            self.uuid_cache.set(row.uuid, row.label);
        }

        Ok(())
    }

    async fn auth(&self, req: &approck::server::Request) -> granite::Result<IdentityStruct> {
        use approck_postgres::App as _a;
        use auth_fence::App as _b;
        let auth_fence = self.auth_fence_system();

        let mut redis = match self.redis.get_dbcx().await {
            Ok(redis) => redis,
            Err(e) => {
                return Err(granite::process_error!(
                    "Error getting Redis connection: {}",
                    e
                ));
            }
        };

        let auth_fence = match auth_fence
            .get_user_identity(&req.session_token(), &mut redis)
            .await
        {
            Ok(user_info) => user_info,
            Err(_e) => {
                approck::error!("Error getting user info: {:?}", _e);
                None
            }
        };

        let dbcx = self.postgres_dbcx().await?;

        let rrr_admin = match &auth_fence {
            Some(user_info) => {
                let row = granite::pg_row_option!(
                    db = dbcx;
                    args = {
                        $identity_uuid: &user_info.identity_uuid,
                    };
                    row = {
                        perm_agent_read: bool,
                        perm_agent_write: bool,
                    };
                    SELECT
                        perm_agent_read,
                        perm_agent_write
                    FROM
                        rrr_admin.identity
                    WHERE TRUE
                        AND identity_uuid = $identity_uuid::uuid
                )
                .await?;

                row.map(|row| RrrAdminIdentity {
                    agent_read: row.perm_agent_read,
                    agent_write: row.perm_agent_write,
                })
            }
            None => None,
        };

        let rrr_agent = match &auth_fence {
            Some(user_info) => {
                let rows = granite::pg_row_vec!(
                    db = dbcx;
                    args = {
                        $identity_uuid: &user_info.identity_uuid,
                    };
                    row = {
                        agent_uuid: Uuid,
                    };
                    SELECT
                        agent_uuid
                    FROM
                        rrr.agent
                    WHERE TRUE
                        AND identity_uuid = $identity_uuid::uuid
                )
                .await?;

                let rows: HashMap<granite::Uuid, RrrAgentIdentity> = rows
                    .into_iter()
                    .map(|row| (row.agent_uuid, RrrAgentIdentity {}))
                    .collect();

                if !rows.is_empty() { Some(rows) } else { None }
            }
            None => None,
        };

        Ok(IdentityStruct {
            remote_address: req.remote_ip(),
            session_token: req.session_token(),
            auth_fence,
            rrr_admin,
            rrr_agent,
        })
    }

    fn uuid_to_label(&self, uuid: granite::Uuid) -> Option<String> {
        self.uuid_cache.get(uuid)
    }
}

impl approck::Identity for IdentityStruct {}

impl App for AppStruct {}

impl Identity for IdentityStruct {
    fn web_usage(&self) -> bool {
        true
    }

    fn api_usage(&self) -> bool {
        true
    }
}
