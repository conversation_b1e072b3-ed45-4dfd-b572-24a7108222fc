#[approck::http(GET /advisor/{advisor_uuid:Uuid}/client/{client_uuid:Uuid}/wizard/policy; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use super::super::WizardStep;
        use approck::html;

        use super::super::client_wizard_context;
        use super::client_policy_get;

        let wizard_data = client_wizard_context::call(
            app,
            identity,
            client_wizard_context::Input {
                client_uuid: path.client_uuid,
            },
        )
        .await?;

        let output = client_policy_get::call(
            app,
            identity,
            client_policy_get::Input {
                client_uuid: path.client_uuid,
            },
        )
        .await?;

        doc.set_title("Policy Details");

        let mut form_wizard = bux::component::form_wizard::new(WizardStep::Policy, wizard_data)?;
        form_wizard.disable_auto_complete();
        form_wizard.add_heading("Policy Details");
        form_wizard.set_id("policy-editor");
        form_wizard.set_hidden("advisor_uuid", path.advisor_uuid);
        form_wizard.set_hidden("client_uuid", path.client_uuid);

        form_wizard.add_body(html!(
            grid-2 #splitter {
                #question-list {
                    panel {
                        content {
                            // Question 1: Monthly Total Premium
                            (bux::input::text::currency::currency_input_with_help("policy_total_premium", "What is your total monthly premium payment for this policy?", output.policy_total_premium, "*This is the total amount you will pay each month for your whole life insurance policy, including both base premium and PUA."))
                        }
                    }
                    panel {
                        content {
                            // PUA split dropdown - only PolicyOnly clients reach this screen
                            (bux::input::select::nilla::nilla_select("pua_split", "PUA Split", &[
                                ("None", "100% Premium / 0% PUA (Insurance Only)"),
                                ("Max", "50% Premium / 50% PUA"),
                            ], output.pua_split.as_deref()))
                            small { "*The split of the premium between PUA and Base Premium. PUA is the amount that will be added to your policy's cash value each month, while the Base Premium is the amount that will be paid to the insurance company." }
                        }
                    }
                    panel {
                        content {
                            // Question 3: One Time PUA Contribution
                            (bux::input::text::currency::currency_input_with_help("pua_contribution", "Would you like to make an additional one-time PUA contribution to jumpstart your cash value?", output.pua_contribution, "*This optional lump sum will be added to your first premium payment and immediately increases your policy's cash value and future growth potential."))
                        }
                    }
                }
                // Information display
                #total-container {
                    div {
                        h3 { "Policy Structure Guide" }
                        hr;
                        p { "Optimize your whole life insurance policy for maximum cash value growth." }
                    }
                    p { "Your monthly premium is split between base coverage (death benefit) and Paid-Up Additions (PUA) which accelerate cash value accumulation." }
                    p { "Higher PUA percentages mean faster cash value growth and more available funds for future borrowing against your policy." }
                    p { "The one-time PUA contribution provides an immediate boost to your cash value, giving you a head start on building your financial foundation." }
                }
            }
        ));

        doc.hide_page_nav();
        doc.add_body(html! {
            (form_wizard)
        });
        Ok(Response::HTML(doc.into()))
    }
}

#[approck::api]
pub mod client_policy_get {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub client_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub pua_split: Option<String>,
        pub pua_contribution: Option<Decimal>,
        pub policy_total_premium: Option<Decimal>,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity.client_read(&dbcx, input.client_uuid).await {
            return_authorization_error!("identity.client_read({})", input.client_uuid);
        }

        let wizard = df4l_zero::client::wizard::Wizard::load(app, &dbcx, input.client_uuid).await?;

        Ok(Output {
            pua_split: wizard.pua_input.pua_split,
            pua_contribution: wizard.pua_input.pua_contribution,
            policy_total_premium: wizard.pua_input.monthly_total_premium,
        })
    }
}

#[approck::api]
pub mod client_policy_set {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub client_uuid: Uuid,
        pub budget_pua_split: PuaSplit,
        pub budget_pua_contribution: Decimal,
        pub policy_total_premium: Decimal,
    }

    #[granite::gtype(ApiInput)]
    pub enum PuaSplit {
        Max,
        None,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {}

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Response> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity.client_write(&dbcx, input.client_uuid).await {
            return_authorization_error!("identity.client_write({})", input.client_uuid);
        }

        let budget_pua_split = match input.budget_pua_split {
            PuaSplit::Max => "Max",
            PuaSplit::None => "None",
        };

        granite::pg_execute!(
            db = dbcx;
            args = {
                $client_uuid: &input.client_uuid,
                $budget_pua_split: &budget_pua_split,
                $budget_pua_contribution: &input.budget_pua_contribution,
                $policy_total_premium: &input.policy_total_premium,
            };
            UPDATE
                df4l.client
            SET
                budget_pua_split = $budget_pua_split,
                budget_pua_contribution = $budget_pua_contribution,
                policy_total_premium = $policy_total_premium
            WHERE
                client_uuid = $client_uuid
        )
        .await?;

        Ok(Response::Output(Output {}))
    }
}
