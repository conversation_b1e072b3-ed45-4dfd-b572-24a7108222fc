#[approck::http(GET /advisor/{advisor_uuid:Uuid}/client/{client_uuid:Uuid}/wizard/report; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        // imports and dependencies
        use super::super::WizardStep;
        use super::super::client_wizard_context;
        use approck::html;

        // document configuration

        // data fetching
        let wizard_data = client_wizard_context::call(
            app,
            identity,
            client_wizard_context::Input {
                client_uuid: path.client_uuid,
            },
        )
        .await?;

        // TODO: if this is a pattern we want to use, then it needs to have an identity and security check
        let wizard_core = df4l_zero::client::wizard::Wizard::load(
            app,
            &app.postgres_dbcx().await?,
            path.client_uuid,
        )
        .await?;

        // wizard setup and configuration
        let wizard = {
            let mut wizard = bux::component::form_wizard::new(WizardStep::Report, wizard_data)?;
            wizard.set_id("report");
            wizard.set_hidden("client_uuid", path.client_uuid);

            if let Ok(debts) = wizard_core.debts_result {
                wizard.add_heading("Don't Forget the Problem");
                wizard.add_description(&format!(
                    "Why is this a problem? You’re paying {monthly_payment_total}/month across these {debt_count} debts — but {monthly_interest_total} goes to interest. That means only {monthly_principal_total} actually reduces your balances. You keep working, but the bank keeps most of it. That’s the trap we solve.",
                    monthly_payment_total = bux::format_currency_us_0(debts.monthly_payment_total),
                    debt_count = debts.debts.len(),
                    monthly_interest_total = bux::format_currency_us_0(debts.monthly_interest_total),
                    monthly_principal_total = bux::format_currency_us_0(debts.monthly_principal_total)
                ));

                // main layout structure
                wizard.add_body(html!(
                    df4l-advisor-debt-free {
                        @if let Some(percent_that_is_interest) = debts.percent_that_is_interest {
                            div.subtitle {
                                (format!("{} of your total payments go to interest", bux::format_percentage_us_0(percent_that_is_interest)))
                            }
                            div {
                                @let principal_percent = granite::Decimal::new(100,0) - percent_that_is_interest;
                                progress-bar {
                                    progress-used style=(format!("width: {}%;", percent_that_is_interest)) {}
                                    progress-remaining style=(format!("width: {}%;", principal_percent)) {}
                                }
                                progress-details {
                                    span { "Interest" }
                                    span { "Principal" }
                                }
                            }
                        }
                        @else {
                            div.subtitle {
                                "You are not paying any interest! Amazing!"
                            }
                        }

                        table {
                            thead {
                                tr {
                                    th.x-name { "Debt Name" }
                                    th.x-balance { "Balance" }
                                    th.x-rate { "Interest Rate" }
                                    th.x-payment { "Monthly Payment" }
                                    th.x-interest { "Interest Portion" }
                                }
                            }
                            tbody {
                                @for debt in &debts.debts {
                                    tr {
                                        td.x-name { (debt.name) }
                                        td.x-balance { (bux::format_currency_us_0(debt.balance)) }
                                        td.x-rate { (bux::format_percentage_us_0(debt.annual_interest_percentage)) }
                                        td.x-payment { (bux::format_currency_us_0(debt.monthly_payment_amount)) }
                                        td.x-interest {
                                            @if let Some(percent_that_is_interest) = debt.percent_that_is_interest {
                                                @let principal_percent = granite::Decimal::new(100,0) - percent_that_is_interest;
                                                div.progress-bar-container {
                                                    div style="flex: 1;" {
                                                        progress-bar {
                                                            progress-used style=(format!("width: {}%;", percent_that_is_interest)) {}
                                                            progress-remaining style=(format!("width: {}%;", principal_percent)) {}
                                                        }
                                                        div.bar-labels {
                                                            span { (format!("{}% Interest", percent_that_is_interest.round_dp(0))) }
                                                            span { (format!("{}% Principal", principal_percent.round_dp(0))) }
                                                        }
                                                    }
                                                    div.tooltip-wrapper style="align-self: flex-start; padding-top: 4px;" {
                                                        i."fas fa-info-circle info-icon-inline" {}
                                                        div.tooltiptext {
                                                            "Out of your " 
                                                            (bux::format_currency_us_0(debt.monthly_payment_amount))
                                                            " monthly payment, " 
                                                            (bux::format_currency_us_0(debt.monthly_interest_amount))
                                                            " goes to interest and only " (bux::format_currency_us_0(debt.monthly_principal_amount)) 
                                                            " reduces your balance. You're paying " (percent_that_is_interest.round_dp(0)) 
                                                            "% to the bank and keeping just " 
                                                            (principal_percent.round_dp(0))
                                                            "% for yourself."
                                                        }
                                                    }
                                                }
                                            } @else {
                                                "N/A"
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    // print section
                    div.print-section {
                        a.btn.primary onclick="window.print()" {
                            i.fas.fa-print {}
                            " Print"
                        }
                    }
                ));
            }
            wizard
        };

        // document finalization
        doc.set_title("Debt Free When?");
        doc.hide_page_nav();
        doc.add_body(html!((wizard)));
        Ok(Response::HTML(doc.into()))
    }
}
