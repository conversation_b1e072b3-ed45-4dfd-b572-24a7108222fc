#[approck::http(GET /advisor/{advisor_uuid:Uuid}/client/{client_uuid:Uuid}/wizard/dump; AUTH None; return HTML;)]
pub mod page {
    use granite::return_authorization_error;

    pub async fn request(
        app: App,
        identity: Identity,
        doc: DocumentPlain,
        path: Path,
    ) -> Result<Response> {
        use approck::html;

        let dbcx = app.postgres_dbcx().await?;
        if !identity.client_read(&dbcx, path.client_uuid).await {
            return_authorization_error!("identity.client_read({})", path.client_uuid);
        }

        let wizard = df4l_zero::client::wizard::Wizard::load(app, &dbcx, path.client_uuid).await?;

        let crunch_output = match wizard.to_policy_output() {
            Ok(output) => output,
            Err(err) => {
                doc.add_body(html!(
                    pre {
                        (maud::PreEscaped(format!("{:#?}", err)))
                    }
                ));
                return Ok(Response::HTML(doc.into()));
            }
        };

        doc.add_body(html!(
            pre {
                (maud::PreEscaped(format!("{:#?}", crunch_output)))
            }
        ));
        Ok(Response::HTML(doc.into()))
    }
}
