#[approck::http(GET /advisor/{advisor_uuid:Uuid}/client/{client_uuid:Uuid}/wizard/budget; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use super::super::WizardStep;
        use approck::html;

        use super::super::client_wizard_context;
        use super::client_budget_get;

        let wizard_context = client_wizard_context::call(
            app,
            identity,
            client_wizard_context::Input {
                client_uuid: path.client_uuid,
            },
        )
        .await?;

        let output = client_budget_get::call(
            app,
            identity,
            client_budget_get::Input {
                client_uuid: path.client_uuid,
            },
        )
        .await?;

        doc.set_title("Set Your Client's Budget");
        let wizard = {
            let mut wizard = bux::component::form_wizard::new(WizardStep::Budget, wizard_context)?;
            wizard.set_id("budget-entry");
            wizard.add_heading("Set Your Client's Budget");
            wizard.add_description(
                "Please provide information about your client's current financial situation.",
            );
            wizard.set_hidden("client_uuid", path.client_uuid);
            wizard.add_body(html!(
                grid-2 #splitter {
                    #question-list {
                        panel {
                            content {
                                // Question 1: Extra debt payments
                                (bux::input::text::currency::currency_input_with_help("budget_extra_debts", "Are you making extra payments on any of your current debts, including your mortgage? If so, how much are you paying extra per month?", output.budget_extra_debts, "*Enter $0 if you are not making extra payments."))
                            }
                        }
                        panel {
                            content {
                                // Question 2: Retirement contributions
                                (bux::input::text::currency::currency_input_with_help("budget_extra_retirement", "Are you putting money into a retirement plan that is NOT being matched? If so, how much are you contributing per month?", output.budget_extra_retirement, "*Enter $0 if you are not contributing to a retirement plan."))
                            }
                        }
                        panel {
                            content {
                                // Question 3: Regular savings
                                (bux::input::text::currency::currency_input_with_help("budget_extra_savings", "Are you putting money into savings on a regular basis? If so, how much are you saving per month?", output.budget_extra_savings, "*Enter $0 if you are not saving."))
                            }
                        }
                        panel {
                            content {
                                // Question 4: Mortgage program affordability
                                (bux::input::text::currency::currency_input_with_help("budget_extra_surplus", "If we can show you how to get your mortgage paid off faster could you afford to put extra money towards this program without it affecting your lifestyle? If so, how much could you afford per month?", output.budget_extra_surplus, "*Enter $0 if you are not able to afford extra money."))
                            }
                        }
                    }
                    // Total display (calculated client-side)
                    #total-container {
                        div {
                            h3 { "Total: " span #total { "$0.00" } "/month" }
                            hr;
                            p { "This is the total amount of money you are already spending which can be redirected into your custom Debt2Captial™ Policy, helping you get out of debt faster and build capital for future purchases." }
                        }
                        p { "Using this approach we are working with you to build a debt elimination plan that is already within your monthly budget adding no additional out of pocket expense negatively affecting your monthly cash flow."}

                        div.gray-border {
                            (bux::input::text::currency::currency_input_with_help("pua_contribution", "One Time PUA Contribution", output.budget_pua_contribution, "*An optional one-time contribution to the PUA amount. This will be added to the first month's premium and increase the amount added to your policy's cash value."))
                        }
                    }
                }
            ));
            wizard
        };

        doc.hide_page_nav();
        doc.add_body(html! {
            (wizard)
        });
        Ok(Response::HTML(doc.into()))
    }
}

#[approck::api]
pub mod client_budget_get {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub client_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub budget_extra_debts: Option<Decimal>,
        pub budget_extra_savings: Option<Decimal>,
        pub budget_extra_retirement: Option<Decimal>,
        pub budget_extra_surplus: Option<Decimal>,
        pub budget_pua_split: Option<String>,
        pub budget_pua_contribution: Option<Decimal>,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity.client_read(&dbcx, input.client_uuid).await {
            return_authorization_error!("identity.client_read({})", input.client_uuid);
        }

        let wizard = df4l_zero::client::wizard::Wizard::load(app, &dbcx, input.client_uuid).await?;
        let row = wizard.budget_input;

        Ok(Output {
            budget_extra_debts: row.budget_extra_debts,
            budget_extra_savings: row.budget_extra_savings,
            budget_extra_retirement: row.budget_extra_retirement,
            budget_extra_surplus: row.budget_extra_surplus,
            budget_pua_split: row.budget_pua_split,
            budget_pua_contribution: row.budget_pua_contribution,
        })
    }
}

#[approck::api]
pub mod client_budget_set {
    use df4l_zero::client::wizard::ClientType;
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub client_uuid: Uuid,
        pub budget_extra_debts: Option<Decimal>,
        pub budget_extra_savings: Option<Decimal>,
        pub budget_extra_retirement: Option<Decimal>,
        pub budget_extra_surplus: Option<Decimal>,
        pub budget_pua_split: Option<String>,
        pub budget_pua_contribution: Option<Decimal>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {}

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity.client_write(&dbcx, input.client_uuid).await {
            return_authorization_error!("identity.client_write({})", input.client_uuid);
        }

        // Load wizard to get client type
        let wizard = df4l_zero::client::wizard::Wizard::load(app, &dbcx, input.client_uuid).await?;

        // For DebtManagement clients, automatically set budget_pua_split to "Max"
        let budget_pua_split = match wizard.client_type {
            ClientType::DebtManagement => Some("Max".to_string()),
            ClientType::PolicyOnly => input.budget_pua_split.clone(), // Keep user input for PolicyOnly
        };

        // Calculate policy_total_premium as the sum of all 4 budget fields
        let policy_total_premium = input.budget_extra_debts.unwrap_or_default()
            + input.budget_extra_savings.unwrap_or_default()
            + input.budget_extra_retirement.unwrap_or_default()
            + input.budget_extra_surplus.unwrap_or_default();

        granite::pg_execute!(
            db = dbcx;
            args = {
                $client_uuid: &input.client_uuid,
                $budget_extra_debts: &input.budget_extra_debts,
                $budget_extra_savings: &input.budget_extra_savings,
                $budget_extra_retirement: &input.budget_extra_retirement,
                $budget_extra_surplus: &input.budget_extra_surplus,
                $budget_pua_split: &budget_pua_split,
                $budget_pua_contribution: &input.budget_pua_contribution,
                $policy_total_premium: &policy_total_premium,
            };
            UPDATE
                df4l.client
            SET
                budget_extra_debts = $budget_extra_debts,
                budget_extra_savings = $budget_extra_savings,
                budget_extra_retirement = $budget_extra_retirement,
                budget_extra_surplus = $budget_extra_surplus,
                budget_pua_split = $budget_pua_split,
                budget_pua_contribution = $budget_pua_contribution,
                policy_total_premium = $policy_total_premium
            WHERE
                client_uuid = $client_uuid
        )
        .await?;

        Ok(Output {})
    }
}

#[approck::api]
pub mod client_budget_validate {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub client_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub enum Output {
        Valid,
        Invalid {
            message: String,
            budget_extra_debts: Option<String>,
            budget_extra_savings: Option<String>,
            budget_extra_retirement: Option<String>,
            budget_extra_surplus: Option<String>,
            budget_pua_split: Option<String>,
            budget_pua_contribution: Option<String>,
        },
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity.client_write(&dbcx, input.client_uuid).await {
            return_authorization_error!("identity.client_write({})", input.client_uuid);
        }

        let wizard = df4l_zero::client::wizard::Wizard::load(app, &dbcx, input.client_uuid).await?;

        match wizard.budget_result {
            Ok(_) => Ok(Output::Valid),
            Err(budget_error) => Ok(Output::Invalid {
                message: budget_error.message,
                budget_extra_debts: budget_error.budget_extra_debts,
                budget_extra_savings: budget_error.budget_extra_savings,
                budget_extra_retirement: budget_error.budget_extra_retirement,
                budget_extra_surplus: budget_error.budget_extra_surplus,
                budget_pua_split: budget_error.budget_pua_split,
                budget_pua_contribution: budget_error.budget_pua_contribution,
            }),
        }
    }
}
