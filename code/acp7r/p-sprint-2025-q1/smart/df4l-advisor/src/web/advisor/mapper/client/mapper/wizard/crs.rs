#[approck::http(GET /advisor/{advisor_uuid:Uuid}/client/{client_uuid:Uuid}/wizard/crs; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use super::super::WizardStep;
        use df4l_zero::types::CreditReportSubject;

        use approck::html;

        doc.set_title("Credit Report");

        use super::super::client_wizard_context;
        use super::render_applicant_panel;

        let client_uuid = path.client_uuid;

        let wizard_data = client_wizard_context::call(
            app,
            identity,
            client_wizard_context::Input {
                client_uuid: path.client_uuid,
            },
        )
        .await?;

        let wizard = {
            let mut wizard = bux::component::form_wizard::new(WizardStep::Crs, wizard_data)?;
            wizard.set_hidden("client_uuid", path.client_uuid);
            wizard.add_heading("Connect Credit Report");

            let applicant_content = render_applicant_panel::call(
                app,
                identity,
                render_applicant_panel::Input {
                    client_uuid,
                    credit_report_subject: CreditReportSubject::Applicant,
                },
            )
            .await?;

            let spouse_content = render_applicant_panel::call(
                app,
                identity,
                render_applicant_panel::Input {
                    client_uuid,
                    credit_report_subject: CreditReportSubject::Spouse,
                },
            )
            .await?;

            wizard.add_body(html!(
                div.df4l-advisor-onboarding-crs.bux-narrow-md {
                    input type="hidden" name="client_uuid" value=(path.client_uuid);

                    grid-2 {
                        div #Applicant {
                            (maud::PreEscaped(applicant_content.inner_html))
                        }

                        div #Spouse {
                            (maud::PreEscaped(spouse_content.inner_html))
                        }
                    }
                }
            ));
            wizard
        };

        doc.hide_page_nav();
        doc.add_body(html!(
            div.constrain-width-md {
                (wizard)
            }
        ));
        Ok(Response::HTML(doc.into()))
    }
}

#[approck::api]
pub mod render_applicant_panel {
    use approck::html;
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub client_uuid: Uuid,
        pub credit_report_subject: CreditReportSubject,
    }

    #[granite::gtype(ApiInput, ts_from = "@df4l-zero/typesλ.mts")]
    pub type CreditReportSubject = ::df4l_zero::types::CreditReportSubject;

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub inner_html: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity.client_read(&dbcx, input.client_uuid).await {
            return_authorization_error!("identity.client_read({})", input.client_uuid);
        }

        let credit_report_subject_info = df4l_zero::client::get_credit_report_subject_info(
            &dbcx,
            input.client_uuid,
            input.credit_report_subject,
        )
        .await?;

        let label = match input.credit_report_subject {
            CreditReportSubject::Applicant => "Primary Applicant",
            CreditReportSubject::Spouse => "Spouse",
        };

        let markup = match (
            credit_report_subject_info.enabled,
            credit_report_subject_info.api_crs_user_uuid,
        ) {
            // Not started at all, but not disabled -- only actions are to start or to disable
            (None, _) | (Some(true), None) => {
                html! {
                    h3 { (label) }
                    label { "Credit report process has not been started. Click below to begin the secure identity verification and credit report process." }
                    button.primary { "Get Started" }
                    a href="#deactivate" { "Disable Soft Pull" }
                }
            }

            // Disabled -- only action is to enable
            (Some(false), _) => {
                html! {
                    h3 { (label) }
                    label { "Credit report soft pull has been disabled." }
                    a href="#activate" { "Enable Soft Pull" }
                }
            }

            (Some(true), Some(api_crs_user_uuid)) => {
                // load the user and get the state
                // TODO: pass identity
                let user = api_crs::core::user::load(&dbcx, api_crs_user_uuid).await?;

                match user {
                    api_crs::core::user::User::NotCreated(_) => {
                        html! {
                            h3 { (label) }
                            label { "Personal information is needed to begin the credit report process. This includes name, address, SSN, and date of birth for identity verification." }
                            button.primary { "Resume Process" }
                            a href="#deactivate" { "Disable Soft Pull" }
                        }
                    }
                    api_crs::core::user::User::Created(_) => {
                        html! {
                            h3 { (label) }
                            label { "Identity verification is required through a secure mobile authentication process. A verification code will be sent to the provided mobile number." }
                            button.primary { "Resume Process" }
                            a href="#deactivate" { "Disable Soft Pull" }
                        }
                    }
                    api_crs::core::user::User::Verified(_) => {
                        html! {
                            h3 { (label) }
                            label { "Identity verification is complete. Ready to perform a soft credit report pull. This will not affect the credit score." }
                            button.primary { "Resume Process" }
                            a href="#deactivate" { "Disable Soft Pull" }
                        }
                    }
                    api_crs::core::user::User::WithReport(_) => {
                        html! {
                            h3 { (label) }
                            label { "Credit report soft pull is complete! The report is available for review and includes VantageScore® 3.0 using Equifax data." }
                            div.mt-2 {
                                label-tag.success { "Complete" }
                                a href="#deactivate" { "Disable Soft Pull" }
                            }
                        }
                    }
                }
            }
        };

        Ok(Output {
            inner_html: markup.into(),
        })
    }
}

#[approck::api]
pub mod get_api_crs_user_uuid {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub client_uuid: Uuid,
        pub credit_report_subject: CreditReportSubject,
    }

    #[granite::gtype(ApiInput, ts_from = "@df4l-zero/typesλ.mts")]
    pub type CreditReportSubject = ::df4l_zero::types::CreditReportSubject;

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub api_crs_user_uuid: Uuid,
        pub inner_html: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let mut dbcx = app.postgres_dbcx().await?;

        if !identity.client_read(&dbcx, input.client_uuid).await {
            return_authorization_error!("identity.client_read({})", input.client_uuid);
        }

        let credit_report_subject_info = df4l_zero::client::get_credit_report_subject_info(
            &dbcx,
            input.client_uuid,
            input.credit_report_subject,
        )
        .await?;

        let api_crs_user_uuid = match credit_report_subject_info.api_crs_user_uuid {
            Some(api_crs_user_uuid) => api_crs_user_uuid,
            None => {
                // create the user
                df4l_zero::client::allocate_api_crs_user(
                    &mut dbcx,
                    input.client_uuid,
                    input.credit_report_subject,
                )
                .await?
            }
        };

        let inner_html = super::render_applicant_panel::call(
            app,
            identity,
            super::render_applicant_panel::Input {
                client_uuid: input.client_uuid,
                credit_report_subject: input.credit_report_subject,
            },
        )
        .await?
        .inner_html;

        Ok(Output {
            api_crs_user_uuid,
            inner_html,
        })
    }
}

#[approck::api]
pub mod refresh_panel {
    use super::render_applicant_panel;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub client_uuid: Uuid,
        pub credit_report_subject: CreditReportSubject,
    }

    #[granite::gtype(ApiInput, ts_from = "@df4l-zero/typesλ.mts")]
    pub type CreditReportSubject = ::df4l_zero::types::CreditReportSubject;

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub inner_html: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let inner_html = render_applicant_panel::call(
            app,
            identity,
            render_applicant_panel::Input {
                client_uuid: input.client_uuid,
                credit_report_subject: input.credit_report_subject,
            },
        )
        .await?
        .inner_html;

        Ok(Output { inner_html })
    }
}

#[approck::api]
pub mod client_toggle {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub client_uuid: Uuid,
        pub credit_report_subject: CreditReportSubject,
        pub active: bool,
    }

    #[granite::gtype(ApiInput, ts_from = "@df4l-zero/typesλ.mts")]
    pub type CreditReportSubject = ::df4l_zero::types::CreditReportSubject;

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub inner_html: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity.client_write(&dbcx, input.client_uuid).await {
            return_authorization_error!("identity.client_write({})", input.client_uuid);
        }

        df4l_zero::client::toggle_api_crs_user(
            &dbcx,
            input.client_uuid,
            input.credit_report_subject,
            input.active,
        )
        .await?;

        let inner_html = super::render_applicant_panel::call(
            app,
            identity,
            super::render_applicant_panel::Input {
                client_uuid: input.client_uuid,
                credit_report_subject: input.credit_report_subject,
            },
        )
        .await?
        .inner_html;

        Ok(Output { inner_html })
    }
}

#[approck::api]
pub mod validate {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub client_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub enum Output {
        Ok,
        Error {
            applicant_error: Option<String>,
            spouse_error: Option<String>,
        },
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity.client_read(&dbcx, input.client_uuid).await {
            return_authorization_error!("identity.client_read({})", input.client_uuid);
        }

        // load the wizard
        let wizard = df4l_zero::client::wizard::Wizard::load(app, &dbcx, input.client_uuid).await?;

        match wizard.crs_result {
            Ok(_) => Ok(Output::Ok),
            Err(crs_error) => Ok(Output::Error {
                applicant_error: crs_error.applicant_error,
                spouse_error: crs_error.spouse_error,
            }),
        }
    }
}
