
form.bux-form-wizard {
    & > header {
        margin-bottom: 0 !important;

        & h1 {
            color: red;
        }
    }

    /* Main report layout container */
    df4l-advisor-debt-free {

        .subtitle {
            text-align: center;
            font-weight: bold;
            font-size: 1.4rem;
            margin: 1.5rem 0 .5rem;
        }

        progress-bar {
            height: 25px;
            background: #e9ecef;
            border-radius: 15px;
            overflow: hidden;
            display: flex;

            progress-used {
                background: linear-gradient(to bottom, #e57373, #f37a77);
                transition: width 0.3s ease;
                display: block;
                height: 100%;
            }

            progress-remaining {
                background: linear-gradient(to bottom, #81c784, #75d37a);
                transition: width 0.3s ease;
                display: block;
                height: 100%;
            }
        }

        progress-details {
            display: flex;
            justify-content: space-between;
            font-size: 11pt;
            color: #666;
            margin-bottom: 2rem;
        }

        .tooltip-wrapper {
            position: relative;
            display: inline-block;
            cursor: pointer;

            .tooltiptext {
                visibility: hidden;
                width: 300px;
                background: #fff;
                color: #000;
                text-align: left;
                border-radius: 6px;
                padding: 1rem;
                border: 1px solid #ccc;
                position: absolute;
                z-index: 1;
                top: 0;
                right: 105%;
                font-size: 0.9rem;
                line-height: 1.5;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

                &::after {
                    content: "";
                    position: absolute;
                    top: 10px;
                    left: 100%;
                    margin-left: 0px;
                    border-width: 5px;
                    border-style: solid;
                    border-color: transparent transparent transparent #ccc;
                }
            }

            &:hover .tooltiptext {
                visibility: visible;
            }
        }

        .info-icon-inline {
            color: #007bff;
            font-size: 1rem;
        }

        .bar-labels {
            display: flex;
            justify-content: space-between;
            font-size: 10pt;
            margin-top: 0.25rem;
            color: #333;
        }

        .progress-bar-container {
            display: flex;
            align-items: flex-start;
            justify-content: space-between;
            gap: 0.5rem;
        }

        table {
            .x-balance {
                text-align: right;
            }

            .x-rate {
                text-align: right;
            }

            .x-payment {
                text-align: right;
            }

            th:last-child,
            td:last-child {
                width: 40%;
            }
        }
    }
    .print-section {
        display: flex;
        justify-content: center;
    }
}

/* Print Report Functionality - Consolidated print styles */
@media print {
    /* 1. Expand the default content container */
    content-container {
        width: 100% !important;
        max-width: 100% !important;
        padding: 0 !important;
        margin: 0 auto !important;
        overflow: visible !important;
    }

    /* 2. Hide UI clutter (navs, buttons, layout shells) */
    split-button,
    .btn, .btn-wrapper, .dropdown-wrapper,
    .breadcrumb, page-nav-wrapper, nav-wrapper, nav, footer,
    button[onclick="window.print()"] {
        display: none !important;
    }

    /* Hide form wizard header when printing */
    form.bux-form-wizard > header {
        display: none !important;
    }

    /* Panel page break rules for long content */
    panel {
        page-break-inside: avoid;
        page-break-after: auto;
    }

    /* Ensure panels don't break awkwardly */
    content {
        page-break-inside: avoid;
    }

    /* Ensure all report content is visible and printable */
    df4l-advisor-debt-free {
        display: block !important;
        visibility: visible !important;
    }

    /* Prevent tables from breaking awkwardly */
    table {
        page-break-inside: avoid;
    }

    /* Preserve all colors as-is when printing */
    * {
        -webkit-print-color-adjust: exact !important;
        print-color-adjust: exact !important;
    }
}
