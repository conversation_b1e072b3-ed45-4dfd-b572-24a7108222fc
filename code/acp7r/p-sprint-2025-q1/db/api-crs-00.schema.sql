-- log should have entries every time we do anything with crs on behalf of a user, and anything we do with crs in general (like refresh tokens).  Do not put sensitive data into the log.

CREATE SCHEMA IF NOT EXISTS api_crs;

CREATE TABLE api_crs.user (
   api_crs_user_uuid uuid DEFAULT public.uuidv7() NOT NULL,
   crs_user_uuid uuid null,
   create_ts timestamptz(6) NOT NULL DEFAULT now(),
   first_name varchar(64),
   last_name varchar(64),
   email varchar(255),
   phone varchar(20),
   verify_ts timestamptz(6),
   last_report_esid varchar(64),
   last_report_ts timestamptz(6),
   data_hash varchar(64),
   CONSTRAINT user_pkey PRIMARY KEY (api_crs_user_uuid),
   CONSTRAINT valid_state CHECK (

      -- NotCreated
      (
         crs_user_uuid IS NULL 
         AND verify_ts IS NULL
      ) 
      
      OR
      
      -- Created
      (
         crs_user_uuid IS NOT NULL 
         AND verify_ts IS NULL

         AND first_name IS NOT NULL
         AND last_name IS NOT NULL
         AND email IS NOT NULL
         AND phone IS NOT NULL
      )

      OR

      -- Verified
      (
         crs_user_uuid IS NOT NULL 
         AND verify_ts IS NOT NULL

         AND first_name IS NOT NULL
         AND last_name IS NOT NULL
         AND email IS NOT NULL
         AND phone IS NOT NULL
      )
   )
);

