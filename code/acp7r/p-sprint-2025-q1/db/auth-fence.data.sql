--------------------------------------
-- auth_fence database initial data --
--------------------------------------

INSERT INTO "auth_fence"."identity" ("identity_uuid", "identity_type", "create_ts", "name", "email", "note", "avatar_uri", "active") VALUES ('01938e7d-8401-7e9a-93ab-06efa55747a6', 'User', '2024-12-03 21:46:50.750767+00', '<PERSON>', '<EMAIL>', NULL, NULL, 't');
INSERT INTO "auth_fence"."identity" ("identity_uuid", "identity_type", "create_ts", "name", "email", "note", "avatar_uri", "active") VALUES ('01938e7d-8403-7458-8bdf-3d5246667a06', 'User', '2024-12-03 21:46:50.750767+00', '<PERSON><PERSON>', '<EMAIL>', NULL, NULL, 't');
INSERT INTO "auth_fence"."identity" ("identity_uuid", "identity_type", "create_ts", "name", "email", "note", "avatar_uri", "active") VALUES ('01938e7d-8403-735a-b8cf-abe2e282e52b', 'User', '2024-12-03 21:46:50.750767+00', 'Mikee Berdin', '<EMAIL>', NULL, NULL, 't');
INSERT INTO "auth_fence"."identity" ("identity_uuid", "identity_type", "create_ts", "name", "email", "note", "avatar_uri", "active") VALUES ('01938e7d-8403-7854-b14a-96de2df9b5a4', 'User', '2024-12-03 21:46:50.750767+00', 'Rachelle Flores', '<EMAIL>', NULL, NULL, 't');
INSERT INTO "auth_fence"."identity" ("identity_uuid", "identity_type", "create_ts", "name", "email", "note", "avatar_uri", "active") VALUES ('01938e7d-8403-7a47-8adf-cd57724a6184', 'User', '2024-12-03 21:46:50.750767+00', 'Andrew Bidochko', '<EMAIL>', NULL, NULL, 't');
INSERT INTO "auth_fence"."identity" ("identity_uuid", "identity_type", "create_ts", "name", "email", "note", "avatar_uri", "active") VALUES ('01938e7d-8403-7f1f-b87b-6f28e6f3d581', 'User', '2024-12-03 21:46:50.750767+00', 'Edissa Aringo', '<EMAIL>', NULL, NULL, 't');
INSERT INTO "auth_fence"."identity" ("identity_uuid", "identity_type", "create_ts", "name", "email", "note", "avatar_uri", "active") VALUES ('01938e7d-8403-7cc9-ab59-e64ddc008b0d', 'User', '2024-12-03 21:46:50.750767+00', 'Iryna Bidochko', '<EMAIL>', NULL, NULL, 't');
INSERT INTO "auth_fence"."identity" ("identity_uuid", "identity_type", "create_ts", "name", "email", "note", "avatar_uri", "active") VALUES ('01938e7d-8403-7980-9e79-8d064a55a56c', 'User', '2024-12-03 21:46:50.750767+00', 'James Garber', '<EMAIL>', NULL, NULL, 't');
INSERT INTO "auth_fence"."identity" ("identity_uuid", "identity_type", "create_ts", "name", "email", "note", "avatar_uri", "active") VALUES ('01938e7d-8403-7a49-8adf-cd57724a6186', 'User', '2024-12-03 21:46:50.750767+00', 'Elizabeth Garber', '<EMAIL>', NULL, NULL, 't');
INSERT INTO "auth_fence"."identity" ("identity_uuid", "identity_type", "create_ts", "name", "email", "note", "avatar_uri", "active") VALUES ('01938e7d-8403-7a50-8adf-cd57724a6187', 'User', '2024-12-03 21:46:50.750767+00', 'Ezra Garber', '<EMAIL>', NULL, NULL, 't');
INSERT INTO "auth_fence"."identity" ("identity_uuid", "identity_type", "create_ts", "name", "email", "note", "avatar_uri", "active") VALUES ('01938e7d-8403-7a51-8adf-cd57724a6188', 'User', '2024-12-03 21:46:50.750767+00', 'Geraldine Barcelon', '<EMAIL>', NULL, NULL, 't');
INSERT INTO "auth_fence"."identity" ("identity_uuid", "identity_type", "create_ts", "name", "email", "note", "avatar_uri", "active") VALUES ('01938e7d-8403-7a52-8adf-cd57724a6189', 'User', '2024-12-03 21:46:50.750767+00', 'Jeff Berdin', '<EMAIL>', NULL, NULL, 't');
INSERT INTO "auth_fence"."identity" ("identity_uuid", "identity_type", "create_ts", "name", "email", "note", "avatar_uri", "active") VALUES ('01938e7d-8403-7a53-8adf-cd57724a6190', 'User', '2024-12-03 21:46:50.750767+00', 'Luke Frisken', '<EMAIL>', NULL, NULL, 't');
INSERT INTO "auth_fence"."identity" ("identity_uuid", "identity_type", "create_ts", "name", "email", "note", "avatar_uri", "active") VALUES ('01938e7d-8403-7a55-8adf-cd57724a6192', 'User', '2024-12-03 21:46:50.750767+00', 'Eli Garber', '<EMAIL>', NULL, NULL, 't');
INSERT INTO "auth_fence"."identity" ("identity_uuid", "identity_type", "create_ts", "name", "email", "note", "avatar_uri", "active") VALUES ('01938e7d-8403-7a56-8adf-cd57724a6193', 'User', '2024-12-03 21:46:50.750767+00', 'Julie Garber', '<EMAIL>', NULL, NULL, 't');
INSERT INTO "auth_fence"."identity" ("identity_uuid", "identity_type", "create_ts", "name", "email", "note", "avatar_uri", "active") VALUES ('01938e7d-8403-7a57-8adf-cd57724a6194', 'User', '2024-12-03 21:46:50.750767+00', 'Sergio Olivio', '<EMAIL>', NULL, NULL, 't');
INSERT INTO "auth_fence"."identity" ("identity_uuid", "identity_type", "create_ts", "name", "email", "note", "avatar_uri", "active") VALUES ('01938e7d-8403-7a58-8adf-cd57724a6195', 'User', '2024-12-03 21:46:50.750767+00', 'Zach August', '<EMAIL>', NULL, NULL, 't');
INSERT INTO "auth_fence"."identity" ("identity_uuid", "identity_type", "create_ts", "name", "email", "note", "avatar_uri", "active") VALUES ('01938e7d-8403-7a59-8adf-cd57724a6196', 'User', '2024-12-03 21:46:50.750767+00', 'Sherrie Size', '<EMAIL>', NULL, NULL, 't');
INSERT INTO "auth_fence"."identity" ("identity_uuid", "identity_type", "create_ts", "name", "email", "note", "avatar_uri", "active") VALUES ('01938e7d-8403-7a59-8adf-cd57724a6197', 'User', '2024-12-03 21:46:50.750767+00', 'Onesmus Nkhoma', '<EMAIL>', NULL, NULL, 't');
INSERT INTO "auth_fence"."identity" ("identity_uuid", "identity_type", "create_ts", "name", "email", "note", "avatar_uri", "active") VALUES ('01938e7d-8403-7a59-8adf-cd57724a6198', 'User', '2024-12-03 21:46:50.750767+00', 'Jimmy Ngandani', '<EMAIL>', NULL, NULL, 't');

INSERT INTO "auth_fence"."identity_key" ("identity_key_uuid", "identity_uuid", "create_ts", "create_note", "secret_sha256", "secret_salt") VALUES ('01938e81-93a2-741b-9544-836c0cc583ce', '01938e7d-8401-7e9a-93ab-06efa55747a6', '2024-12-03 21:51:16.895936+00', NULL, 'c8b2505b76926abdc733523caa9f439142f66aa7293a7baaac0aed41a191eef6', 'salt');
INSERT INTO "auth_fence"."identity_key" ("identity_key_uuid", "identity_uuid", "create_ts", "create_note", "secret_sha256", "secret_salt") VALUES ('01938e82-4819-73cd-aaf0-409b15786416', '01938e7d-8403-7980-9e79-8d064a55a56c', '2024-12-03 21:52:03.096396+00', NULL, 'c8b2505b76926abdc733523caa9f439142f66aa7293a7baaac0aed41a191eef6', 'salt');


INSERT INTO "auth_fence"."ssopro" 
  ("ssopro_xsid", "name", "description", "active")
VALUES
  ('google', 'Google', 'Sign-in with Google', true),
  ('microsoft', 'Microsoft', 'Sign-in with Microsoft', true)
;

-----------------------------------------------------------------
INSERT INTO "auth_fence"."permission" (
  "permission_uuid", "permission_psid", "func", "args", "create_ts", "name", "description"
) VALUES
  ('a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11', 'identity.read', 'identity_read', '{}', '2024-12-03 21:46:50.750767+00', 'View Identity Data', 'List and view identity records'),
  ('a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12', 'identity.write', 'identity_write', '{}', '2024-12-03 21:46:50.750767+00', 'Update Identities', 'Update identity records'),
  ('a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a13', 'identity.add', 'identity_add', '{}', '2024-12-03 21:46:50.750767+00', 'Create Identities', 'Create new identity records'),
  ('a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a14', 'identity.perm', 'identity_manage', '{}', '2024-12-03 21:46:50.750767+00', 'Role & Permission Management', 'Manage roles and permissions');


INSERT INTO "auth_fence"."role" (
  "role_uuid", "role_psid", "create_ts", "name"
) VALUES
  ('b0eebc99-9c0b-4ef8-bb6d-6bb9bd380b15', 'moderator', '2024-12-03 21:46:50.750767+00', 'Moderator'),
  ('b0eebc99-9c0b-4ef8-bb6d-6bb9bd380b16', 'administrator', '2024-12-03 21:46:50.750767+00', 'Administrator');


INSERT INTO
  "auth_fence"."role_permission" (
    "role_uuid", "permission_uuid", "create_ts"
  ) VALUES
  ('b0eebc99-9c0b-4ef8-bb6d-6bb9bd380b15', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11', '2024-12-03 21:46:50.750767+00'),
  ('b0eebc99-9c0b-4ef8-bb6d-6bb9bd380b15', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12', '2024-12-03 21:46:50.750767+00'),
  ('b0eebc99-9c0b-4ef8-bb6d-6bb9bd380b16', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11', '2024-12-03 21:46:50.750767+00'),
  ('b0eebc99-9c0b-4ef8-bb6d-6bb9bd380b16', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12', '2024-12-03 21:46:50.750767+00'),
  ('b0eebc99-9c0b-4ef8-bb6d-6bb9bd380b16', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a13', '2024-12-03 21:46:50.750767+00'),
  ('b0eebc99-9c0b-4ef8-bb6d-6bb9bd380b16', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a14', '2024-12-03 21:46:50.750767+00');


INSERT INTO
  "auth_fence"."identity_role" (
    "identity_uuid", "role_uuid", "create_ts"
  ) VALUES
  ('01938e7d-8401-7e9a-93ab-06efa55747a6', 'b0eebc99-9c0b-4ef8-bb6d-6bb9bd380b16', '2024-12-03 21:46:50.750767+00'),
  ('01938e7d-8403-7980-9e79-8d064a55a56c', 'b0eebc99-9c0b-4ef8-bb6d-6bb9bd380b16', '2024-12-03 21:46:50.750767+00'),
  ('01938e7d-8403-7458-8bdf-3d5246667a06', 'b0eebc99-9c0b-4ef8-bb6d-6bb9bd380b15', '2024-12-03 21:46:50.750767+00'),
  ('01938e7d-8403-735a-b8cf-abe2e282e52b', 'b0eebc99-9c0b-4ef8-bb6d-6bb9bd380b15', '2024-12-03 21:46:50.750767+00');


