# ACP7R Project - Claude Code Context

## Project Overview

ACP7R is a comprehensive Rust-based web application framework and monorepo containing multiple applications and modules. The project focuses on building scalable web applications with a consistent architecture.

### Core Values
- Consistency over flexibility
- Minimize boilerplate 
- Convention over configuration
- Avoid unnecessary complexity

## Technology Stack

### Primary Languages
- **Rust (Edition 2024)**: Backend framework with latest async trait features
- **TypeScript**: Generated frontend code for type-safe client development
- **CSS**: Generated and compiled styles with optimization
- **HTML**: Generated via Maud templating system

### Frameworks & Libraries
- **approck**: Main application framework with modules like `approck-postgres` and `approck-redis`
- **bux**: User interface framework for building web applications
- **granite**: Foundation utilities and error handling system

### What NOT to Use
- `bootstrap` (UI framework)
- `react` (frontend framework)
- `anyhow` (error handling)
- `error_stack` (error handling)
- `this_error` (error handling)
- Do not add new dependencies without approval

## Architecture

### Crate Types
1. **Regular crates**: Standard Rust crates
2. **Application crates**: Have `[package.metadata.acp]` with `app` key in Cargo.toml
3. **Module crates**: Have `[package.metadata.acp]` with `module` key in Cargo.toml

### Application Structure
Applications follow a consistent pattern:
- Each app lives in a subfolder (e.g., `appcove`, `smart`, `reo`)
- Short alphanumeric crate names (e.g., `df4l`, `aplay`, `reo`)
- Top-level app crate + feature-specific sub-crates
- Sub-crates named `{app}-{feature}` (e.g., `df4l-admin`, `df4l-public`)
- Each app has a `{app}-zero` crate for shared types and utilities

### Module System
- Applications extend modules via `extends` key in Cargo.toml
- Modules can extend other modules
- Common modules: `auth-fence`, `api-sentry`, `api-stripe`, `api-twilio`, `msg-io`

## Development Commands

Use these commands for development:
- `./acp build` - Full build check
- `./acp build -p <app>` - Build specific app
- `./acp check` - Syntax checking
- `./acp format` - Code formatting
- `./acp test` - Run tests
- `./acp test -p <crate>` - Test specific crate
- `./acp workspace` - Workspace info about apps, modules, and crates

## File Search
- `rg ...` - Find code with ripgrep
- `fd ...` - Find files
- `find ...` - Alternative file finding

## Naming Conventions
- **snake_case**: Rust and TypeScript variables and methods
- **CamelCase**: Rust types and TypeScript classes
- **ALL_CAPS**: Rust constants and TypeScript const variables

## Current Work Context

The project is currently working on the `aplay` application in the `appcove` directory, which includes:
- Basic application scaffolding that compiles successfully
- A "bounce" page with canvas and TypeScript game loop
- A "tiletog" game with grid-based drawing canvas and minimap
- Enhanced triangle drawing logic with two-direction and three-direction neighbor patterns

## Application Directory Structure

Applications must follow this structure:
- `src/lib.rs` - Main application code
- `src/libλ.rs` - Generated route handling code
- `src/libλ.ts` - Generated TypeScript code
- `src/main.rs` - Entry point with `approck::main!()` macro
- `src/module/mod.rs` - Module index
- `src/module/{module_name}.rs` - Module implementations
- `src/web/Document.rs` - HTML document structure
- `src/web/Document.ts` - TypeScript for document
- `src/web/Document.mcss` - Document styles

## Key Traits

### Application Traits
```rust
pub trait App {}
pub trait Identity {
    fn web_usage(&self) -> bool { true }
    fn api_usage(&self) -> bool { true }
}
pub trait Document {}
```

Every application must define:
1. `pub struct AppConfig` (with serde::Deserialize)
2. `pub struct AppStruct` 
3. `pub struct IdentityStruct`
4. `impl approck::App for AppStruct`
5. `impl approck::Identity for IdentityStruct`

## Testing and Quality Assurance

Always run lint and typecheck commands after making changes:
- Look for `npm run lint`, `npm run typecheck`, `ruff`, or similar commands
- Check README or search codebase to determine the testing approach
- Never assume specific test frameworks - verify what's in use

## Important Notes

- Never commit changes unless explicitly asked
- Always prefer editing existing files over creating new ones
- Follow existing code conventions and patterns
- Use workspace dependencies defined in root Cargo.toml
- Each application needs a unique port assigned in `[package.metadata.acp]`