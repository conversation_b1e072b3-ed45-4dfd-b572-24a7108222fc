#[approck::http(GET /; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.set_title("App Directory");

        doc.add_body(html!(
            panel {
                header {
                    h5 {"App Directory"}
                }
                content {
                    panel {
                        content {
                            grid-2 {
                                list-group.flush {
                                    ul {
                                        li { a href="https://local.acp7.net:3009" target="_reo" { "REO" } }
                                        li { a href="https://local.acp7.net:3014" target="_df4l" { "DF4L" } }
                                        li { a href="https://local.acp7.net:3012" target="_rrr" { "Real Return Reporter" } }
                                        li { a href="https://local.acp7.net:3008" target="_appcove" { "AppCove" } }
                                    }
                                }
                                list-group.flush {
                                    ul {
                                        li { a href="https://local.acp7.net:3013" target="_homewaters" { "Homewaters" } }
                                        li { a href="https://local.acp7.net:3020" target="_approck_example" { "Approck Example" } }
                                        li { a href="https://local.acp7.net:3001" target="_erg4" { "Erg4" } }
                                        li { a href="https://local.acp7.net:3015" target="_pm5" { "PM5" } }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        ));
        Response::HTML(doc.into())
    }
}
