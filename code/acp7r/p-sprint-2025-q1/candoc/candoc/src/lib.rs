#[path = "libλ.rs"]
pub mod libλ;
mod module;
mod web;

pub trait App: approck::App + approck::server::App {}
pub trait Identity: approck::Identity {
    fn web_usage(&self) -> bool {
        true
    }
    fn api_usage(&self) -> bool {
        true
    }
}

pub trait Document: bux::document::Base {}

///////////////////////////////////////////////////////////////////////////////////////////////////

#[derive(serde::Deserialize)]
pub struct AppConfig {
    pub webserver: approck::server::ModuleConfig,
}

pub struct AppStruct {
    pub webserver: approck::server::Module,
}

#[derive(Debug)]
pub struct IdentityStruct {}

pub use crate::web::Document::Document as DocumentStruct;

///////////////////////////////////////////////////////////////////////////////////////////////////

impl approck::App for AppStruct {
    type Config = AppConfig;
    type Identity = IdentityStruct;
    fn new(config: Self::Config) -> granite::Result<Self> {
        use approck::Module;
        Ok(Self {
            webserver: approck::server::Module::new(config.webserver)?,
        })
    }
    async fn init(&self) -> granite::Result<()> {
        use approck::Module;
        self.webserver.init().await?;
        // get the crate name using the env! macro
        let crate_name = env!("CARGO_PKG_NAME");
        println!("init: {crate_name}");
        Ok(())
    }

    async fn auth(&self, _req: &approck::server::Request) -> granite::Result<IdentityStruct> {
        Ok(IdentityStruct {})
    }
}

impl approck::Identity for IdentityStruct {}

impl App for AppStruct {}
impl Identity for IdentityStruct {}
impl Document for DocumentStruct {}
