#[approck::http(GET /admin/openai/console; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(app: App, identity: Identity, doc: Document) -> Result<Response> {
        use approck::html;

        let instructions = super::get_instructions::call(app, identity).await?.instructions;

        doc.set_title("OpenAI Console");
        doc.add_body(html!{
            form id="f1" {
                error {}
                (bux::input::textarea::string::name_label_value("instructions", "Instructions:", instructions.as_deref()))
                (bux::input::textarea::string::name_label_value("message", "Message:", None))
                (bux::button::submit::save("Send to OpenAI"))

                hr;
                div #response {}
            }
        });


        Ok(Response::HTML(doc.into()))
    }
}

#[approck::function]
pub mod get_instructions {
    pub struct Input;
    pub struct Output {
        pub instructions: Option<String>,
    }

    use granite::return_authorization_error;
    pub async fn call(app: App, identity: Identity) -> Result<Output> {
        if !identity.is_admin() {
            return_authorization_error!("Only admins can access the OpenAI console");
        }

        let mut redis = app.redis_dbcx().await?;
        let instruction_key = format!("openai:instructions:{}", identity.session_token());
        let instructions = redis.get_val_opt::<String>(&instruction_key).await?;

        Ok(Output { instructions })
    }
}

#[approck::api]
pub mod chat_completion {
    use granite::{return_authorization_error, ResultExt};
    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub instructions: String,
        pub message: String,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub responses: Vec<String>,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        if !identity.is_admin() {
            return_authorization_error!("Only admins can access the OpenAI console");
        }

        // cache instructions in redis by session token
        let mut redis = app.redis_dbcx().await?;
        let instruction_key = format!("openai:instructions:{}", identity.session_token());
        redis.set_val(&instruction_key, &input.instructions).await?;
       

        // Call the OpenAI API
        let responses = app.openai()
            .responses(&input.message, &input.instructions)
            .await
            .amend(|e| {
                e.add_context("Failed to call OpenAI API.")
            })?;

        Ok(Output { responses })
    }
}

