mod openai;
pub mod web;


pub trait App: approck::App + auth_fence::App + approck_redis::App {
    fn openai(&self) -> &ModuleStruct;
}

pub trait Identity: approck::Identity + auth_fence::Identity {
    fn web_usage(&self) -> bool {
        self.is_admin()
    }
    fn api_usage(&self) -> bool {
        self.is_admin()
    }
    fn is_admin(&self) -> bool;
}

pub trait Document: bux::document::Base {}

#[derive(serde::Deserialize)]
pub struct ModuleConfig {
    pub api_key: String,
}

pub struct ModuleStruct {
    pub api_key: String,
}

impl approck::Module for ModuleStruct {
    type Config = ModuleConfig;
    fn new(config: Self::Config) -> granite::Result<Self> {
        Ok(Self {
            api_key: config.api_key,
        })
    }
    async fn init(&self) -> granite::Result<()> {
        Ok(())
    }
}

impl ModuleStruct {
    pub async fn chat_completion(
        &self,
        model: &str,
        message: &str,
    ) -> granite::Result<String> {
        let client = reqwest::Client::new();

        let payload = serde_json::json!({
            "model": model,
            "messages": [
                {
                    "role": "user",
                    "content": message
                }
            ],
            "max_completion_tokens": 1000
        });

        approck::info!("OpenAI request: {payload}");

        let response = client
            .post("https://api.openai.com/v1/chat/completions")
            .header("Authorization", format!("Bearer {}", self.api_key))
            .header("Content-Type", "application/json")
            .json(&payload)
            .send()
            .await
            .map_err(|e| granite::Error::api_request_error(format!("Failed to call OpenAI API: {e}")))?;

        if response.status().is_success() {
            let response_json: serde_json::Value = response
                .json()
                .await
                .map_err(|e| granite::Error::api_request_error(format!("Failed to parse OpenAI response: {e}")))?;

            let content = response_json
                .get("choices")
                .and_then(|choices| choices.get(0))
                .and_then(|choice| choice.get("message"))
                .and_then(|message| message.get("content"))
                .and_then(|content| content.as_str())
                .ok_or_else(|| granite::Error::api_request_error("Invalid OpenAI response format".to_string()))?;

            approck::info!("OpenAI response: {response_json}");

            Ok(content.to_string())
        } else {
            let status = response.status();
            let body = response
                .text()
                .await
                .unwrap_or_else(|_| "Unable to read response body".to_string());

            approck::info!("OpenAI API error {status}: {body}");

            Err(granite::Error::api_request_error(format!(
                "OpenAI API error {status}: {body}"
            )))
        }
    }

    pub async fn responses(&self, input_text: &str, instructions: &str) -> granite::Result<Vec<String>> {
        let client = reqwest::Client::new();
        let url = "https://api.openai.com/v1/responses";
        let payload = serde_json::json!({
            "model": "gpt-5-nano",
            "input": input_text,
            "instructions": instructions
        });

        approck::info!("OpenAI request: {payload}");

        let response = client
            .post(url)
            .header("Authorization", format!("Bearer {}", self.api_key))
            .header("Content-Type", "application/json")
            .json(&payload)
            .send()
            .await
            .map_err(|e| granite::Error::api_request_error(format!("Failed to call OpenAI API: {e}")))?;

        if response.status().is_success() {
            let json_response: serde_json::Value = response
                .json()
                .await
                .amend(|e| {
                    e.add_context("Failed to read response text from API call.")
                })?;

            approck::info!("OpenAI response: {json_response:#?}");

            let parsed_response: ResponseOpenAI = serde_json::from_value(json_response).amend(|e| {
                e.add_context("Failed to parse OpenAI response.")
            })?;

            let mut responses = Vec::new();
            for output in parsed_response.output {
                if let Some(content) = output.content {
                    for content in content {
                        responses.push(content.text);
                    }
                }
            }

            Ok(responses)
        } else {
            let status = response.status();
            let body = response
                .text()
                .await
                .unwrap_or_else(|_| "Unable to read response body".to_string());

            approck::info!("OpenAI API error {status}: {body}");

            Err(granite::Error::api_request_error(format!(
                "OpenAI API error {status}: {body}"
            )))
        }
    }

}


use granite::ResultExt;
use serde::Deserialize;

#[derive(Debug, Deserialize)]
pub struct ResponseOpenAI {
    pub error: Option<serde_json::Value>,
    pub id: String,
    pub output: Vec<ResponseOutput>,
}

#[derive(Debug, Deserialize)]
pub struct ResponseOutput {
    pub id: String,
    #[serde(rename = "type")]
    pub output_type: String,
    pub content: Option<Vec<ResponseOutputContent>>,
}

#[derive(Debug, Deserialize)]
pub struct ResponseOutputContent {
    pub text: String,
}

