//-------------------------------------------------------------------------------------------------
// 1. Import Components

import "./dialog2.mcss";
import "@bux/input/text/string.mts";
import "@bux/input/text/currency.mts";
import "@bux/input/select/nilla.mts";
import "@addr-iso/input/address_us_select.mts";

//-------------------------------------------------------------------------------------------------
// 2. Import Code

import { HS, SEC, unwrap_or_null } from "@granite/lib.mts";

import {
    create_form_get,
    create_form_set,
    create_processing,
    direct_get_preauth,
    get_latest_report,
    loading,
    set_verified,
    verify_form_get,
    verify_processing,
} from "./dialog2λ.mts";

import BuxInputTextString from "@bux/input/text/string.mts";
import BuxInputDate from "@bux/input/date.mts";
import BuxInputSelectGender from "@bux/input/select/gender.mts";
import BuxInputSelectNilla from "@bux/input/select/nilla.mjs";
import { call_user_preauth_token } from "@crate/core/crs/user_preauth_token.mts";
import { call_user_dit_identity } from "@crate/core/crs/user_dit_identity.mts";
import { call_user_smfa_send_link } from "@crate/core/crs/user_smfa_send_link.mts";
import { call_user_smfa_verify_status } from "@crate/core/crs/user_smfa_verify_status.mts";

//-------------------------------------------------------------------------------------------------
// 3. Define transition structs

type ToFatalError = {
    api_crs_user_uuid: string;
    error_message: string;
};

type ToLoading = {
    api_crs_user_uuid: string;
};

type ToCreateForm = {
    is_development: boolean;
    api_crs_user_uuid: string;
};

type ToCreateProcessing = {
    is_development: boolean;
    api_crs_user_uuid: string;
    first_name: string;
    last_name: string;
    email: string;
    phone: string;
};

type ToCreateError = {
    is_development: boolean;
    api_crs_user_uuid: string;
    error_message: string;
    first_name: string;
    last_name: string;
    email: string;
    phone: string;
};

type ToVerifyForm = {
    is_development: boolean;
    api_crs_user_uuid: string;
    crs_user_uuid: string;
    first_name: string;
    last_name: string;
    email: string;
    phone: string;
};

type ToVerifyProcessing = verify_processing.ToVerifyProcessing;

type ToVerifyError = {
    is_development: boolean;
    api_crs_user_uuid: string;
    first_name: string;
    last_name: string;
    email: string;
    phone: string;
    error_message: string;
};

type ToSmfaSendForm = {
    is_development: boolean;
    api_crs_user_uuid: string;
    crs_user_uuid: string;
    first_name: string;
    last_name: string;
    email: string;
    phone: string;
    crs_api_base_url: string;
    crs_mobile_number: string;
    crs_user_token: string;
    crs_mobile_token: string;
};

type ToSmfaSendCall = {
    is_development: boolean;
    api_crs_user_uuid: string;
    first_name: string;
    last_name: string;
    email: string;
    phone: string;
    crs_api_base_url: string;
    crs_mobile_number: string;
    crs_user_token: string;
    crs_mobile_token: string;
};

type ToSmfaCheckForm = {
    is_development: boolean;
    api_crs_user_uuid: string;
    first_name: string;
    last_name: string;
    email: string;
    phone: string;
    crs_api_base_url: string;
    crs_mobile_number: string;
    crs_user_token: string;
    crs_smfa_token: string;
};

type ToSmfaCheckCall = {
    is_development: boolean;
    api_crs_user_uuid: string;
    first_name: string;
    last_name: string;
    email: string;
    phone: string;
    crs_api_base_url: string;
    crs_user_token: string;
    crs_mobile_number: string;
    crs_smfa_token: string;
};

type ToVerified = {
    is_development: boolean;
    api_crs_user_uuid: string;
    first_name: string;
    last_name: string;
    email: string;
    phone: string;
};

type ToReportProcessing = {
    is_development: boolean;
    api_crs_user_uuid: string;
    first_name: string;
    last_name: string;
    email: string;
    phone: string;
};

type ToReportError = {
    is_development: boolean;
    api_crs_user_uuid: string;
    error_message: string;
    first_name: string;
    last_name: string;
    email: string;
    phone: string;
};

type ToReportSuccess = {
    is_development: boolean;
    api_crs_user_uuid: string;
    first_name: string;
    last_name: string;
    email: string;
    phone: string;
};

//-------------------------------------------------------------------------------------------------
// 4. Define Dialog

export class Dialog {
    private $dialog: HTMLDialogElement;
    private $devmode: HTMLElement;
    private $titlebar: HTMLElement;
    private $titlebar_h3: HTMLHeadingElement;
    private $error: HTMLElement;
    private $body: HTMLElement;
    public on_state_change: () => void;

    constructor(api_crs_user_uuid: string) {
        this.$dialog = document.createElement("dialog");
        this.$dialog.id = "api-crs-dialog2";

        this.$titlebar = document.createElement("header");
        this.$devmode = document.createElement("dev-mode");
        this.$titlebar_h3 = document.createElement("h3");
        this.$titlebar.appendChild(this.$titlebar_h3);

        {
            const $close = document.createElement("a");
            $close.classList.add();
            $close.innerHTML = '<i class="far fa-times-circle fa-lg"></i>';
            $close.addEventListener("click", () => {
                this.$dialog.close();
                this.$dialog.remove();
            });
            this.$titlebar.appendChild($close);
        }

        this.$error = document.createElement("error");
        this.$body = document.createElement("content");

        this.$dialog.appendChild(this.$titlebar);
        this.$dialog.appendChild(this.$devmode);
        this.$dialog.appendChild(this.$error);
        this.$dialog.appendChild(this.$body);

        document.body.appendChild(this.$dialog);

        to_loading(this, { api_crs_user_uuid });

        this.$dialog.showModal();
        this.on_state_change = () => {};
    }

    public set_title(title: string): void {
        this.$titlebar_h3.textContent = title;
    }

    public set_body(element: HTMLElement | string): void {
        if (typeof element === "string") {
            this.$error.innerHTML = "";
            this.$body.textContent = element;
            return;
        } else {
            this.$error.innerHTML = "";
            this.$body.replaceChildren(element);
        }
    }

    public set_error(e: HTMLElement | string | null): void {
        if (e === null) {
            this.$error.innerHTML = "";
            return;
        }

        if (typeof e === "string") {
            this.$error.innerText = e;
        }

        this.$error.replaceChildren(e);
    }

    public trigger_on_state_change() {
        this.on_state_change();
    }

    public close() {
        this.$dialog.close();
        this.$dialog.remove();
    }

    public set_devmode(): void {
        this.$devmode.textContent = "DEVELOPMENT MODE";
    }

    // Helper function to manage dialog width
    public set_width(isFullWidth: boolean) {
        if (isFullWidth) {
            this.$dialog.classList.add("full-width");
        } else {
            this.$dialog.classList.remove("full-width");
        }
    }
}

// -------------------------------------------------------------------------------------------------
// 5. Define Transitions

function to_fatal_error(dlg: Dialog, trans: ToFatalError) {
    console.error(trans.error_message);
    dlg.set_title("Connection Error");
    dlg.set_width(false);
    const $html = document.createElement("div");
    $html.innerHTML = `
        <x-errors>
            <p><strong>We're experiencing technical difficulties</strong></p>
            <p>We apologize for the inconvenience. Please try again in a few moments, or <a href="/help" target="_blank">contact support</a> if the problem persists.</p>
        </x-errors>

        <dl>
            <dt>User ID</dt>
            <dd>${HS(trans.api_crs_user_uuid)}</dd>
            <dt>Error</dt>
            <dd><pre>${HS(trans.error_message)}</pre></dd>
        </dl>
    `;
    dlg.set_body($html);
}

async function to_loading(dlg: Dialog, trans: ToLoading) {
    dlg.set_title("Loading...");
    dlg.set_width(false);
    const $html = document.createElement("div");
    $html.innerHTML = "Please wait while the content is loaded...";
    dlg.set_body($html);

    const response = await loading.call({
        api_crs_user_uuid: trans.api_crs_user_uuid,
    });

    if (!("Output" in response)) {
        to_fatal_error(dlg, {
            api_crs_user_uuid: trans.api_crs_user_uuid,
            error_message: JSON.stringify(response),
        });
        return;
    }

    const output = response.Output[0];

    if ("ToCreateForm" in output) {
        const d = output.ToCreateForm;
        if (d.is_development) {
            dlg.set_devmode();
        }
        return to_create_form(dlg, {
            is_development: d.is_development,
            api_crs_user_uuid: d.api_crs_user_uuid,
        });
    }

    if ("ToVerifyForm" in output) {
        const d = output.ToVerifyForm;
        if (d.is_development) {
            dlg.set_devmode();
        }
        return to_verify_form(dlg, {
            is_development: d.is_development,
            api_crs_user_uuid: d.api_crs_user_uuid,
            crs_user_uuid: d.crs_user_uuid,
            first_name: d.first_name,
            last_name: d.last_name,
            email: d.email,
            phone: d.phone,
        });
    }

    if ("ToVerified" in output) {
        const d = output.ToVerified;
        if (d.is_development) {
            dlg.set_devmode();
        }
        return to_verified(dlg, {
            is_development: d.is_development,
            api_crs_user_uuid: d.api_crs_user_uuid,
            first_name: d.first_name,
            last_name: d.last_name,
            email: d.email,
            phone: d.phone,
        });
    }

    if ("ToWithReport" in output) {
        const d = output.ToWithReport;
        if (d.is_development) {
            dlg.set_devmode();
        }
        return to_report_success(dlg, {
            is_development: d.is_development,
            api_crs_user_uuid: d.api_crs_user_uuid,
            first_name: d.first_name,
            last_name: d.last_name,
            email: d.email,
            phone: d.phone,
        });
    }

    if ("ToInactive" in output) {
        const d = output.ToInactive;
        if (d.is_development) {
            dlg.set_devmode();
        }
        return to_fatal_error(dlg, {
            api_crs_user_uuid: d.api_crs_user_uuid,
            error_message: "Soft Credit Pull has been disabled.",
        });
    }
}

async function to_create_form(dlg: Dialog, trans: ToCreateForm) {
    const response = await create_form_get.call({ api_crs_user_uuid: trans.api_crs_user_uuid });
    if (!("Output" in response)) {
        to_fatal_error(dlg, {
            api_crs_user_uuid: trans.api_crs_user_uuid,
            error_message: "Failed to load create form",
        });
        return;
    }

    const output = response.Output[0];

    const $html = document.createElement("div");
    $html.innerHTML = output.form_html;

    // find elements
    const $form = SEC(HTMLFormElement, $html, "form");
    const $first_name = SEC(BuxInputTextString, $form, "[name=first_name]");
    const $last_name = SEC(BuxInputTextString, $form, "[name=last_name]");
    const $email = SEC(BuxInputTextString, $form, "[name=email]");
    const $phone = SEC(BuxInputTextString, $form, "[name=phone]");

    $form.addEventListener("submit", (event) => {
        event.preventDefault();

        // create full input struct
        const r = create_form_set.Input_Partial_validate({
            api_crs_user_uuid: trans.api_crs_user_uuid,
            first_name: $first_name.value,
            last_name: $last_name.value,
            email: $email.value,
            phone: $phone.value,
        });

        if ("Err" in r) {
            dlg.set_error(r.Err.Outer);
            if ("Inner" in r.Err) {
                const d = r.Err.Inner;
                $first_name.set_e(d.first_name);
                $last_name.set_e(d.last_name);
                $email.set_e(d.email);
                $phone.set_e(d.phone);
            }
            $form.reportValidity();
            return;
        }

        create_form_set.call(r.Ok).then((response) => {
            if ("Output" in response) {
                const output = response.Output[0];
                to_create_processing(dlg, {
                    is_development: trans.is_development,
                    api_crs_user_uuid: trans.api_crs_user_uuid,
                    first_name: output.first_name,
                    last_name: output.last_name,
                    email: output.email,
                    phone: output.phone,
                });
                return;
            }

            if ("ValidationError" in response) {
                const e = response.ValidationError[0];
                dlg.set_error(e.Outer);
                if ("Inner" in e) {
                    $first_name.set_e(e.Inner.first_name);
                    $last_name.set_e(e.Inner.last_name);
                    $email.set_e(e.Inner.email);
                    $phone.set_e(e.Inner.phone);
                }
                $form.reportValidity();
                return;
            }

            const e = JSON.stringify(response);
            dlg.set_error(e);
            return;
        });
    });

    dlg.set_title("Register");
    dlg.set_body($html);
    dlg.set_width(false);
}

async function to_create_processing(dlg: Dialog, trans: ToCreateProcessing) {
    dlg.set_title("Processing...");
    dlg.set_width(false);
    dlg.set_body("...");

    // invoke create_processing call
    const r = await create_processing.call({
        api_crs_user_uuid: trans.api_crs_user_uuid,
    });

    if ("Output" in r) {
        const output = r.Output[0];

        // Since the state may have changed from create_processing.call(),
        // the underlying UI may need updated
        dlg.trigger_on_state_change();

        return to_verify_form(dlg, {
            is_development: trans.is_development,
            api_crs_user_uuid: trans.api_crs_user_uuid,
            crs_user_uuid: output.crs_user_uuid,
            first_name: trans.first_name,
            last_name: trans.last_name,
            email: trans.email,
            phone: trans.phone,
        });
    }

    if ("Error" in r) {
        const e = r.Error[0];
        return to_create_error(dlg, {
            is_development: trans.is_development,
            api_crs_user_uuid: trans.api_crs_user_uuid,
            error_message: e,
            first_name: trans.first_name,
            last_name: trans.last_name,
            email: trans.email,
            phone: trans.phone,
        });
    }

    return to_fatal_error(dlg, {
        api_crs_user_uuid: trans.api_crs_user_uuid,
        error_message: JSON.stringify(r),
    });
}

function to_create_error(dlg: Dialog, state: ToCreateError) {
    const $html = document.createElement("div");

    $html.innerHTML = `
        <x-errors>
            <p>There was an error creating your account.</p>
            <pre>${HS(state.error_message)}</pre>
        </x-errors>

        <dl>
            <dt>Name: </dt>
            <dd>${HS(state.first_name)} ${HS(state.last_name)}</dd>
            <dt>Email: </dt>
            <dd>${HS(state.email)}</dd>
            <dt>Phone: </dt>
            <dd>${HS(state.phone)}</dd>
            <dt>User ID: </dt>
            <dd>${HS(state.api_crs_user_uuid)}</dd>
        </dl>

        <x-button-container>
            <a href="/help" target="_blank" class="primary">Contact Support</a>
            <button name="back" class="secondary">Back</button>
        </x-button-container>
    `;

    const $back_button = SEC(HTMLButtonElement, $html, "button[name=back]");
    $back_button.addEventListener("click", (event) => {
        event.preventDefault();
        return to_create_form(dlg, {
            is_development: state.is_development,
            api_crs_user_uuid: state.api_crs_user_uuid,
        });
    });

    dlg.set_title("Registration Error");
    dlg.set_body($html);
}

async function to_verify_form(dlg: Dialog, trans: ToVerifyForm) {
    const r = await verify_form_get.call({
        api_crs_user_uuid: trans.api_crs_user_uuid,
    });

    if (!("Output" in r)) {
        to_fatal_error(dlg, {
            api_crs_user_uuid: trans.api_crs_user_uuid,
            error_message: JSON.stringify(r),
        });
        return;
    }

    const output = r.Output[0];

    const $html = document.createElement("div");
    $html.innerHTML = output.form_html;

    // find elements
    const $form = SEC(HTMLFormElement, $html, "form");
    const $first_name = SEC(BuxInputTextString, $html, "[name=first_name]");
    const $last_name = SEC(BuxInputTextString, $html, "[name=last_name]");
    const $email = SEC(BuxInputTextString, $html, "[name=email]");
    const $phone = SEC(BuxInputTextString, $html, "[name=phone]");
    const $birth_date = SEC(BuxInputDate, $html, "[name=birth_date]");
    const $gender = SEC(BuxInputSelectGender, $html, "[name=gender]");
    const $ssn = SEC(BuxInputTextString, $html, "[name=ssn]");
    const $street1 = SEC(BuxInputTextString, $html, "[name=street1]");
    const $street2 = SEC(BuxInputTextString, $html, "[name=street2]");
    const $city = SEC(BuxInputTextString, $html, "[name=city]");
    const $state = SEC(BuxInputSelectNilla, $html, "[name=state]");
    const $zip = SEC(BuxInputTextString, $html, "[name=zip]");

    // These four come from the transition data
    $first_name.set_p(trans.first_name);
    $last_name.set_p(trans.last_name);
    $email.set_p(trans.email);
    $phone.set_p(trans.phone);

    // bind event handlers to dds buttons
    // these elements are only present in development
    if (trans.is_development) {
        const $dds = SEC(HTMLDivElement, $html, "#api-crs-dds");
        for (const $button of Array.from($dds.querySelectorAll("button"))) {
            const ssn = $button.getAttribute("ssn") ?? "";
            const mobile = $button.getAttribute("mobile") ?? "";
            $button.addEventListener("click", (event) => {
                event.preventDefault();
                $first_name.set_p("GERTRUDE");
                $last_name.set_p("HARKENREADEO");
                $email.set_p("<EMAIL>");
                $phone.set_p(mobile);
                $birth_date.set_p(new Date("1967-06-08"));
                $gender.set_p("Female");
                $street1.set_p("305 LINDEN AV");
                $street2.set_p("123");
                $city.set_p("Anytown");
                $state.set_p("GA");
                $zip.set_p("30316");
                $ssn.set_p(ssn);
            });
        }
    }

    $form.addEventListener("submit", (event) => {
        event.preventDefault();
        dlg.set_error(null);

        const r = verify_processing.ToVerifyProcessing_Partial_validate({
            is_development: trans.is_development,
            api_crs_user_uuid: trans.api_crs_user_uuid,
            crs_user_uuid: trans.crs_user_uuid,
            first_name: $first_name.value,
            last_name: $last_name.value,
            email: $email.value,
            phone: $phone.value,
            birth_date: $birth_date.value ?? undefined,
            gender: $gender.value,
            ssn: $ssn.value,
            street1: $street1.value,
            street2: $street2.value_option,
            city: $city.value,
            state: $state.value,
            zip: $zip.value,
        });

        if ("Err" in r) {
            dlg.set_error(r.Err.Outer);
            if ("Inner" in r.Err) {
                $first_name.set_e(r.Err.Inner.first_name);
                $last_name.set_e(r.Err.Inner.last_name);
                $email.set_e(r.Err.Inner.email);
                $phone.set_e(r.Err.Inner.phone);
                $birth_date.set_e(r.Err.Inner.birth_date);
                $gender.set_e(r.Err.Inner.gender);
                $ssn.set_e(r.Err.Inner.ssn);
                $street1.set_e(r.Err.Inner.street1);
                $street2.set_e(r.Err.Inner.street2);
                $city.set_e(r.Err.Inner.city);
                $state.set_e(r.Err.Inner.state);
                $zip.set_e(r.Err.Inner.zip);
            }
            $form.reportValidity();
            return;
        }

        to_verify_processing(dlg, r.Ok);
    });

    dlg.set_title("Verify Your Identity");
    dlg.set_body($html);

    // Set full width for identity verification dialog
    dlg.set_width(true);
}

async function to_verify_processing(dlg: Dialog, trans: ToVerifyProcessing) {
    dlg.set_title("Processing...");
    dlg.set_width(false);
    dlg.set_body("Fetching preauth token...");

    // Get the direct preauth token from the server (which calls crs)
    let crs_preauth_token;
    let crs_api_base_url;
    {
        const r = await direct_get_preauth.call({
            api_crs_user_uuid: trans.api_crs_user_uuid,
        });

        if (!("Output" in r)) {
            return to_verify_error(dlg, {
                is_development: trans.is_development,
                api_crs_user_uuid: trans.api_crs_user_uuid,
                first_name: trans.first_name,
                last_name: trans.last_name,
                email: trans.email,
                phone: trans.phone,
                error_message: ("Error" in r) ? r.Error[0] : JSON.stringify(r),
            });
        }

        const output = r.Output[0];
        crs_preauth_token = output.crs_preauth_token;
        crs_api_base_url = output.crs_api_base_url;
    }

    dlg.set_body("Fetching user token...");

    // Get the user token from the preauth token
    let crs_user_token;
    {
        const r = await call_user_preauth_token({
            api_crs_user_uuid: trans.api_crs_user_uuid,
            crs_api_base_url: crs_api_base_url,
            crs_preauth_token: crs_preauth_token,
        });

        if (!("Ok" in r)) {
            return to_verify_error(dlg, {
                is_development: trans.is_development,
                api_crs_user_uuid: trans.api_crs_user_uuid,
                first_name: trans.first_name,
                last_name: trans.last_name,
                email: trans.email,
                phone: trans.phone,
                error_message: ("Err" in r) ? r.Err.message : JSON.stringify(r),
            });
        }

        const output = r.Ok;
        crs_user_token = output.crs_user_token;
    }

    dlg.set_body("User token obtained!");

    // pause 0.5 seconds
    await new Promise((resolve) => setTimeout(resolve, 500));

    dlg.set_body("Sending identification data...");

    const r = await call_user_dit_identity({
        api_crs_user_uuid: trans.api_crs_user_uuid,
        crs_api_base_url: crs_api_base_url,
        crs_user_token: crs_user_token,
        fname: trans.first_name,
        lname: trans.last_name,
        mobile: trans.phone,
        ssn: trans.ssn,
        dob: trans.birth_date.toISOString().substring(0, 10),
        street1: trans.street1,
        street2: unwrap_or_null(trans.street2),
        city: trans.city,
        state: trans.state,
        zip: trans.zip,
    });

    if ("Err" in r) {
        return to_verify_error(dlg, {
            is_development: trans.is_development,
            api_crs_user_uuid: trans.api_crs_user_uuid,
            first_name: trans.first_name,
            last_name: trans.last_name,
            email: trans.email,
            phone: trans.phone,
            error_message: r.Err.message,
        });
    }

    const ok = r.Ok;

    dlg.set_body("Identification data sent.");

    // pause 0.5 seconds
    await new Promise((resolve) => setTimeout(resolve, 500));

    // transition to smfa_send step
    return to_smfa_send_form(dlg, {
        is_development: trans.is_development,
        api_crs_user_uuid: trans.api_crs_user_uuid,
        crs_user_uuid: trans.crs_user_uuid,
        first_name: trans.first_name,
        last_name: trans.last_name,
        email: trans.email,
        phone: trans.phone,
        crs_api_base_url: crs_api_base_url,
        crs_mobile_number: ok.crs_mobile_number,
        crs_user_token: crs_user_token,
        crs_mobile_token: ok.crs_mobile_token,
    });
}

function to_smfa_send_form(dlg: Dialog, trans: ToSmfaSendForm) {
    const $html = document.createElement("div");
    $html.innerHTML = `
        <x-intro>
            <p>Upon you clicking the "Send Verification Code" button, we will send a message to the mobile number you provided. You will need to complete identity verification on your mobile device.</p>
        </x-intro>

        <dl>
            <dt>Name</dt>
            <dd>${HS(trans.first_name)} ${HS(trans.last_name)}</dd>
            <dt>Mobile</dt>
            <dd>${HS(trans.crs_mobile_number)}</dd>
        </dl>

        <x-notice>
            <p>
                In cooperation with StitchCredit and Equifax, we will attempt to confirm your identity by sending a secure
                mobile link via text message to your mobile phone number ending in ${
        HS(trans.crs_mobile_number.slice(-4))
    }.
                To continue, open this link on your mobile phone and wait for verification to complete.
            </p>
            <p><em>Message and data rates may apply.</em></p>
        </x-notice>

        <x-button-container>
            <button name="send" class="primary">Send Verification Code</button>
            <button name="back" class="secondary">Back</button>
        </x-button-container>
    `;

    const $back_button = SEC(HTMLButtonElement, $html, "button[name=back]");
    const $send_button = SEC(HTMLButtonElement, $html, "button[name=send]");

    $back_button.addEventListener("click", (event) => {
        event.preventDefault();
        return to_verify_form(dlg, {
            is_development: trans.is_development,
            api_crs_user_uuid: trans.api_crs_user_uuid,
            crs_user_uuid: trans.crs_user_uuid,
            first_name: trans.first_name,
            last_name: trans.last_name,
            email: trans.email,
            phone: trans.phone,
        });
    });

    $send_button.addEventListener("click", (event) => {
        event.preventDefault();
        return to_smfa_send_call(dlg, {
            is_development: trans.is_development,
            api_crs_user_uuid: trans.api_crs_user_uuid,
            first_name: trans.first_name,
            last_name: trans.last_name,
            email: trans.email,
            phone: trans.phone,
            crs_api_base_url: trans.crs_api_base_url,
            crs_user_token: trans.crs_user_token,
            crs_mobile_number: trans.crs_mobile_number,
            crs_mobile_token: trans.crs_mobile_token,
        });
    });

    dlg.set_title("Send Verification Code");
    dlg.set_body($html);
}

async function to_smfa_send_call(dlg: Dialog, trans: ToSmfaSendCall) {
    const $html = document.createElement("div");
    $html.innerHTML = `
        <x-intro>
            <p>Please wait while we send the text message</p>
        </x-intro>

        <dl>
            <dt>Name</dt>
            <dd>${HS(trans.first_name)} ${HS(trans.last_name)}</dd>
            <dt>Mobile</dt>
            <dd>${HS(trans.crs_mobile_number)}</dd>
        </dl>
    `;
    dlg.set_title("Sending Verification Code...");
    dlg.set_body($html);

    const r = await call_user_smfa_send_link({
        api_crs_user_uuid: trans.api_crs_user_uuid,
        crs_api_base_url: trans.crs_api_base_url,
        crs_user_token: trans.crs_user_token,
        crs_mobile_token: trans.crs_mobile_token,
    });

    if ("Err" in r) {
        return to_verify_error(dlg, {
            is_development: trans.is_development,
            api_crs_user_uuid: trans.api_crs_user_uuid,
            first_name: trans.first_name,
            last_name: trans.last_name,
            email: trans.email,
            phone: trans.phone,
            error_message: r.Err.message,
        });
    }

    const ok = r.Ok;

    if (trans.is_development) {
        dev_mobile_display(ok.crs_smfa_message);
    }

    return to_smfa_check_form(dlg, {
        is_development: trans.is_development,
        api_crs_user_uuid: trans.api_crs_user_uuid,
        first_name: trans.first_name,
        last_name: trans.last_name,
        email: trans.email,
        phone: trans.phone,
        crs_api_base_url: trans.crs_api_base_url,
        crs_user_token: trans.crs_user_token,
        crs_mobile_number: trans.crs_mobile_number,
        crs_smfa_token: ok.crs_smfa_token,
    });
}

async function to_smfa_check_form(dlg: Dialog, trans: ToSmfaCheckForm) {
    const $html = document.createElement("div");
    $html.innerHTML = `
        <x-intro>
            <p>Please check your mobile telephone to complete the verification process.</p>
        </x-intro>

        <dl>
            <dt>Name</dt>
            <dd>${HS(trans.first_name)} ${HS(trans.last_name)}</dd>
            <dt>Mobile</dt>
            <dd>${HS(trans.crs_mobile_number)}</dd>
        </dl>

        <p>Once you have completed the verification process, click this button:</p>

        <x-button-container>
            <button class="primary">I Have Completed Verification</button>
        </x-button-container>
    `;
    dlg.set_title("Verification Code Sent");
    dlg.set_body($html);

    const $button = SEC(HTMLButtonElement, $html, "button");

    $button.addEventListener("click", (event) => {
        event.preventDefault();
        return to_smfa_check_call(dlg, {
            is_development: trans.is_development,
            api_crs_user_uuid: trans.api_crs_user_uuid,
            first_name: trans.first_name,
            last_name: trans.last_name,
            email: trans.email,
            phone: trans.phone,
            crs_api_base_url: trans.crs_api_base_url,
            crs_user_token: trans.crs_user_token,
            crs_mobile_number: trans.crs_mobile_number,
            crs_smfa_token: trans.crs_smfa_token,
        });
    });
}

async function to_smfa_check_call(dlg: Dialog, trans: ToSmfaCheckCall) {
    dlg.set_title("Checking Verification Status...");
    dlg.set_body("Please wait while we check the verification status (1/2)...");

    {
        const r = await call_user_smfa_verify_status({
            api_crs_user_uuid: trans.api_crs_user_uuid,
            crs_api_base_url: trans.crs_api_base_url,
            crs_user_token: trans.crs_user_token,
            crs_smfa_token: trans.crs_smfa_token,
        });

        console.log(r);
    }

    dlg.set_body("Please wait while we check the verification status (2/2)...");

    // Get the crs preauth token from the server (which calls crs)
    let crs_preauth_token;
    let crs_api_base_url;
    {
        const r = await direct_get_preauth.call({
            api_crs_user_uuid: trans.api_crs_user_uuid,
        });

        if (!("Output" in r)) {
            return to_verify_error(dlg, {
                is_development: trans.is_development,
                api_crs_user_uuid: trans.api_crs_user_uuid,
                first_name: trans.first_name,
                last_name: trans.last_name,
                email: trans.email,
                phone: trans.phone,
                error_message: ("Error" in r) ? r.Error[0] : JSON.stringify(r),
            });
        }

        const output = r.Output[0];
        crs_preauth_token = output.crs_preauth_token;
        crs_api_base_url = output.crs_api_base_url;
    }

    // Take the preauth token and get the user token, along with the idpass
    let idpass;
    {
        const r = await call_user_preauth_token({
            api_crs_user_uuid: trans.api_crs_user_uuid,
            crs_api_base_url: crs_api_base_url,
            crs_preauth_token: crs_preauth_token,
        });

        if (!("Ok" in r)) {
            return to_verify_error(dlg, {
                is_development: trans.is_development,
                api_crs_user_uuid: trans.api_crs_user_uuid,
                first_name: trans.first_name,
                last_name: trans.last_name,
                email: trans.email,
                phone: trans.phone,
                error_message: ("Err" in r) ? r.Err.message : JSON.stringify(r),
            });
        }

        const output = r.Ok;
        idpass = output.crs_idpass;
    }

    // If the idpass is not true, then we failed to verify
    if (!idpass) {
        return to_verify_error(dlg, {
            is_development: trans.is_development,
            api_crs_user_uuid: trans.api_crs_user_uuid,
            first_name: trans.first_name,
            last_name: trans.last_name,
            email: trans.email,
            phone: trans.phone,
            error_message: "Failed to verify identity",
        });
    }

    // Update the server with the fact that we verified
    {
        const r = await set_verified.call({
            api_crs_user_uuid: trans.api_crs_user_uuid,
        });

        if (!("Output" in r)) {
            return to_verify_error(dlg, {
                is_development: trans.is_development,
                api_crs_user_uuid: trans.api_crs_user_uuid,
                first_name: trans.first_name,
                last_name: trans.last_name,
                email: trans.email,
                phone: trans.phone,
                error_message: ("Error" in r) ? r.Error[0] : JSON.stringify(r),
            });
        }

        // Since the state may have changed from set_verified.call(),
        // the underlying UI may need updated
        dlg.trigger_on_state_change();

        // Otherwise we verified
        return to_verified(dlg, {
            is_development: trans.is_development,
            api_crs_user_uuid: trans.api_crs_user_uuid,
            first_name: trans.first_name,
            last_name: trans.last_name,
            email: trans.email,
            phone: trans.phone,
        });
    }
}

function to_verified(dlg: Dialog, trans: ToVerified) {
    const $html = document.createElement("div");
    $html.innerHTML = `
        <x-success>
            <div class="success-icon">🎉</div>
            <h4>Identity Verification Complete!</h4>
            <p>Your identity has been successfully verified through our secure process. The final step is to perform a soft pull of your credit report. This process:</p>
        </x-success>

        <ul>
            <li>Will NOT affect your credit score</li>
            <li>Provides VantageScore® 3.0 using Equifax data</li>
            <li>Takes only a few seconds to complete</li>
            <li>Is authorized under the Fair Credit Reporting Act</li>
        </ul>

        <p>
            To finish the process, please click the button below to perform the soft pull of your credit report.
        </p>

        <x-button-container>
            <button class="primary">Continue to Soft Pull Credit Report</button>
        </x-button-container>
    `;
    dlg.set_title("Verified");
    dlg.set_body($html);

    const $button = SEC(HTMLButtonElement, $html, "button");
    $button.addEventListener("click", (event) => {
        event.preventDefault();
        return to_report_processing(dlg, {
            is_development: trans.is_development,
            api_crs_user_uuid: trans.api_crs_user_uuid,
            first_name: trans.first_name,
            last_name: trans.last_name,
            email: trans.email,
            phone: trans.phone,
        });
    });

    dlg.set_title("Verification Complete!");
    dlg.set_body($html);
}

async function to_report_processing(dlg: Dialog, trans: ToReportProcessing) {
    dlg.set_title("Processing...");
    dlg.set_body("Fetching latest report...");

    const r = await get_latest_report.call({
        api_crs_user_uuid: trans.api_crs_user_uuid,
    });

    if (!("Output" in r)) {
        return to_report_error(dlg, {
            is_development: trans.is_development,
            api_crs_user_uuid: trans.api_crs_user_uuid,
            first_name: trans.first_name,
            last_name: trans.last_name,
            email: trans.email,
            phone: trans.phone,
            error_message: ("Error" in r) ? r.Error[0] : JSON.stringify(r),
        });
    }

    dlg.trigger_on_state_change();

    return to_report_success(dlg, {
        is_development: trans.is_development,
        api_crs_user_uuid: trans.api_crs_user_uuid,
        first_name: trans.first_name,
        last_name: trans.last_name,
        email: trans.email,
        phone: trans.phone,
    });
}

async function to_report_error(dlg: Dialog, trans: ToReportError) {
    const $html = document.createElement("div");

    $html.innerHTML = `
        <x-errors>
            <div class="error-icon">❌</div>
            <h4>Credit Report Retrieval Error</h4>
            <p>${HS(trans.error_message)}</p>
        </x-errors>

        <dl>
            <dt>Name</dt>
            <dd>${HS(trans.first_name)} ${HS(trans.last_name)}</dd>
            <dt>Email</dt>
            <dd>${HS(trans.email)}</dd>
            <dt>Phone</dt>
            <dd>${HS(trans.phone)}</dd>
            <dt>User ID</dt>
            <dd>${HS(trans.api_crs_user_uuid)}</dd>
            <dt>Error</dt>
            <dd><pre>${HS(trans.error_message)}</pre></dd>
        </dl>

        <x-notice>
            <p><strong>Your Rights</strong></p>
            <p>
                You have the right to access your credit information under the Fair Credit Reporting Act.
                If you continue to experience issues, you may contact Equifax directly or file a complaint
                with the Consumer Financial Protection Bureau.
            </p>
        </x-notice>

        <x-button-container>
            <a href="/help" target="_blank" class="primary">Contact Support</a>
            <button name="back" class="secondary">Back</button>
        </x-button-container>
    `;
    dlg.set_title("Credit Report Error");
    dlg.set_body($html);
}

async function to_report_success(dlg: Dialog, _trans: ToReportSuccess) {
    const $html = document.createElement("div");
    $html.innerHTML = `
        <x-success>
            <h4>Report fetched successfully!</h4>
            <p>The process is complete. You may close this dialog.</p>
        </x-success>

        <x-button-container>
            <button name="close" class="secondary">Close</button>
        </x-button-container>
    `;

    const $button = SEC(HTMLButtonElement, $html, "button");
    $button.addEventListener("click", (event) => {
        event.preventDefault();
        dlg.close();
    });

    dlg.set_title("Report Fetched");
    dlg.set_body($html);
}

function to_verify_error(dlg: Dialog, state: ToVerifyError) {
    const $html = document.createElement("div");

    // example only
    const error_lower = state.error_message.toLowerCase();
    let user_message = "Identity verification failed.";
    let suggestion = "Please check your information and try again.";

    if (error_lower.includes("ssn") || error_lower.includes("social")) {
        user_message = "SSN verification failed.";
        suggestion = "Please verify your SSN and try again.";
    } else if (error_lower.includes("address")) {
        user_message = "Address verification failed.";
        suggestion = "Please ensure your address matches your records.";
    } else if (error_lower.includes("date") || error_lower.includes("birth")) {
        user_message = "Birth date verification failed.";
        suggestion = "Please verify your date of birth and try again.";
    } else if (error_lower.includes("timeout") || error_lower.includes("network")) {
        user_message = "Verification timed out.";
        suggestion = "Please check your connection and try again.";
    }

    $html.innerHTML = `
        <x-errors>
            <p>${user_message} ${suggestion}</p>
        </x-errors>

        <dl>
            <dt>Name</dt>
            <dd>${HS(state.first_name)} ${HS(state.last_name)}</dd>
            <dt>Email</dt>
            <dd>${HS(state.email)}</dd>
            <dt>Phone</dt>
            <dd>${HS(state.phone)}</dd>
            <dt>User ID</dt>
            <dd>${HS(state.api_crs_user_uuid)}</dd>
            <dt>Error</dt>
            <dd><pre>${HS(state.error_message)}</pre></dd>
        </dl>

        <x-button-container>
            <a href="/help" target="_blank" class="primary">Contact Support</a>
            <button name="back" class="secondary">Back</button>
        </x-button-container>
    `;

    const $back_button = SEC(HTMLButtonElement, $html, "button[name=back]");
    $back_button.addEventListener("click", (event) => {
        event.preventDefault();
        return to_loading(dlg, {
            api_crs_user_uuid: state.api_crs_user_uuid,
        });
    });

    dlg.set_title("Identity Verification Error");
    dlg.set_body($html);
}

/// Create a new dialog in the form of a mobile phone and show it on the screen
function dev_mobile_display(message: string) {
    const $dialog = document.createElement("dialog");
    $dialog.innerHTML = `
        <style>
            dialog#mobile-phone {
                position: fixed;
                top: 50%;
                left: 50%;
                width: 375px;
                height: 667px;
                margin-left: -187.5px;
                margin-top: -333.5px;
                background: linear-gradient(145deg, #1a1a1a, #2d2d2d);
                border: 8px solid #333;
                border-radius: 40px;
                padding: 60px 20px 40px 20px;
                box-shadow: 0 20px 40px rgba(0,0,0,0.3);
                z-index: 9999;
            }
            
            dialog#mobile-phone::backdrop {
                background: rgba(0,0,0,0.7);
            }
            
            #mobile-screen {
                width: 100%;
                height: 100%;
                background: #000;
                border-radius: 25px;
                padding: 20px;
                color: #fff;
                font-family: -apple-system, BlinkMacSystemFont, sans-serif;
                overflow-y: auto;
                overflow-x: hidden;
                position: relative;
            }
            
            #mobile-header {
                text-align: center;
                padding: 10px 0;
                border-bottom: 1px solid #333;
                margin-bottom: 20px;
            }
            
            #mobile-close {
                position: absolute;
                top: 10px;
                right: 15px;
                background: none;
                border: none;
                color: #007AFF;
                font-size: 18px;
                cursor: pointer;
            }
            
            #mobile-message {
                font-size: 16px;
                line-height: 1.4;
                white-space: pre-wrap;
                word-wrap: break-word;
                overflow-wrap: break-word;
                margin-bottom: 20px;
            }
            
            #mobile-message a {
                color: #007AFF;
                text-decoration: underline;
                word-break: break-all;
            }
            
            #mobile-instruction {
                font-size: 14px;
                color: #999;
                font-style: italic;
                margin-bottom: 20px;
                padding: 10px;
                background: rgba(255,255,255,0.1);
                border-radius: 8px;
            }
            
            #mobile-iframe {
                width: 100%;
                height: 300px;
                border: 1px solid #333;
                border-radius: 8px;
                background: #fff;
            }
        </style>
        
        <div id="mobile-screen">
            <div id="mobile-header">
                <strong>SMS Message</strong>
                <button id="mobile-close">✕</button>
            </div>
            <div id="mobile-message"></div>
            <div id="mobile-instruction">
                This is to simulate a message that the customer would receive. Please click the above link.
            </div>
            <iframe id="mobile-iframe" style="display: none;"></iframe>
        </div>
    `;

    $dialog.id = "mobile-phone";

    const $message = $dialog.querySelector("#mobile-message") as HTMLDivElement;
    const $iframe = $dialog.querySelector("#mobile-iframe") as HTMLIFrameElement;
    const $mobile_message = $dialog.querySelector("#mobile-message") as HTMLDivElement;
    const $mobile_instruction = $dialog.querySelector("#mobile-instruction") as HTMLDivElement;

    // Parse message and extract links
    const urlRegex = /(https?:\/\/[^\s]+)/g;
    const messageWithLinks = message.replace(urlRegex, (url) => {
        return `<a href="#" data-url="${url}">${url}</a>`;
    });

    $message.innerHTML = messageWithLinks;

    // Handle link clicks
    $message.addEventListener("click", (event) => {
        const target = event.target as HTMLElement;
        if (target.tagName === "A" && target.dataset["url"]) {
            event.preventDefault();
            $iframe.src = target.dataset["url"];
            $iframe.style.display = "block";
            $mobile_message.style.display = "none";
            $mobile_instruction.style.display = "none";
        }
    });

    const $close = $dialog.querySelector("#mobile-close") as HTMLButtonElement;
    $close.addEventListener("click", () => {
        $dialog.close();
        $dialog.remove();
    });

    $dialog.addEventListener("close", () => {
        $dialog.remove();
    });

    document.body.appendChild($dialog);
    $dialog.showModal();
}
