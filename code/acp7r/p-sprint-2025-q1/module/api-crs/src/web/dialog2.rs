#[approck::api]
pub mod loading {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub api_crs_user_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub enum Output {
        ToCreateForm {
            is_development: bool,
            api_crs_user_uuid: Uuid,
        },
        ToVerifyForm {
            is_development: bool,
            api_crs_user_uuid: Uuid,
            crs_user_uuid: Uuid,
            first_name: String,
            last_name: String,
            email: String,
            phone: String,
        },
        ToVerified {
            is_development: bool,
            api_crs_user_uuid: Uuid,
            first_name: String,
            last_name: String,
            email: String,
            phone: String,
        },
        ToWithReport {
            is_development: bool,
            api_crs_user_uuid: Uuid,
            first_name: String,
            last_name: String,
            email: String,
            phone: String,
        },
        ToInactive {
            is_development: bool,
            api_crs_user_uuid: Uuid,
        },
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity.user_read(&dbcx, input.api_crs_user_uuid).await {
            return_authorization_error!("identity.user_read({})", input.api_crs_user_uuid);
        }

        let is_development = app.api_crs_module().is_development();

        let user = crate::core::user::load(&dbcx, input.api_crs_user_uuid).await?;

        match user {
            crate::core::user::User::NotCreated(_) => Ok(Output::ToCreateForm {
                is_development,
                api_crs_user_uuid: input.api_crs_user_uuid,
            }),
            crate::core::user::User::Created(created) => Ok(Output::ToVerifyForm {
                is_development,
                api_crs_user_uuid: input.api_crs_user_uuid,
                crs_user_uuid: created.crs_user_uuid,
                first_name: created.first_name,
                last_name: created.last_name,
                email: created.email,
                phone: created.phone,
            }),
            crate::core::user::User::Verified(verified) => Ok(Output::ToVerified {
                is_development,
                api_crs_user_uuid: input.api_crs_user_uuid,
                first_name: verified.first_name,
                last_name: verified.last_name,
                email: verified.email,
                phone: verified.phone,
            }),
            crate::core::user::User::WithReport(with_report) => Ok(Output::ToWithReport {
                is_development,
                api_crs_user_uuid: input.api_crs_user_uuid,
                first_name: with_report.first_name,
                last_name: with_report.last_name,
                email: with_report.email,
                phone: with_report.phone,
            }),
        }
    }
}

#[approck::api]
pub mod create_form_get {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub api_crs_user_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub form_html: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity.user_read(&dbcx, input.api_crs_user_uuid).await {
            return_authorization_error!("identity.user_read({})", input.api_crs_user_uuid);
        }

        // The only user type valid for this is NotCreated
        let user = match crate::core::user::load(&dbcx, input.api_crs_user_uuid).await? {
            crate::core::user::User::NotCreated(not_created) => not_created,
            _ => {
                return Err(granite::Error::new(granite::ErrorType::Unexpected)
                    .add_context(format!("user_uuid: {}", input.api_crs_user_uuid))
                    .set_external_message(
                        "User is not in the correct state for create form".to_string(),
                    ));
            }
        };

        let html = maud::html! {
            form method="dialog" {
                error { }
                (bux::input::text::string::name_label_value("first_name", "First Name", user.first_name.as_deref()))
                (bux::input::text::string::name_label_value("last_name", "Last Name", user.last_name.as_deref()))
                (bux::input::text::string::name_label_value("email", "Email", user.email.as_deref()))
                (bux::input::text::string::name_label_value("phone", "Phone", user.phone.as_deref()))

                x-compliance {
                    p {
                        "By clicking \"Register\" and creating an account you accept StitchCredit's "
                        a href="https://app.termly.io/document/terms-of-service/9423cd69-88eb-4fca-82be-1077f9e84199" target="_blank" { "Terms of Use" }
                        " and "
                        a href="https://app.termly.io/document/privacy-policy/************************************" target="_blank" { "Privacy Policy" }
                        ". StitchCredit does not maintain critical personal data, much less sell or otherwise disclose your personal information to anyone else. You may opt-out of email correspondence, except confirmation emails, which often contain important information about your account."
                    }
                    p {
                        "By clicking \"Register\" you also agree to D2C™ "
                        a href="/terms" target="_blank" { "Terms of Service" }
                        "."
                    }
                }

                div style="text-align: center;" {
                    button.primary type="submit" { "Register" }
                }
            }
        };

        Ok(Output {
            form_html: html.into(),
        })
    }
}

#[approck::api]
pub mod create_form_set {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub api_crs_user_uuid: Uuid,
        pub first_name: String,
        pub last_name: String,
        pub email: String,
        pub phone: String,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub first_name: String,
        pub last_name: String,
        pub email: String,
        pub phone: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Response> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity.user_write(&dbcx, input.api_crs_user_uuid).await {
            return_authorization_error!("identity.user_write({})", input.api_crs_user_uuid);
        }

        let first_name = input.first_name.trim().to_string();
        let last_name = input.last_name.trim().to_string();
        let email = input.email.trim().to_string();
        let phone = input.phone.trim().to_string();

        let mut error = Input_Error {
            api_crs_user_uuid: None,
            first_name: None,
            last_name: None,
            email: None,
            phone: None,
        };

        if first_name.is_empty() {
            error.first_name = Some("First name is required.".to_string());
        }

        if last_name.is_empty() {
            error.last_name = Some("Last name is required.".to_string());
        }

        if email.is_empty() {
            error.email = Some("Email is required.".to_string());
        } else if !granite::validate_email(&email) {
            error.email = Some("Invalid email address.".to_string());
        }

        if phone.is_empty() {
            error.phone = Some("Phone is required.".to_string());
        } else if !granite::validate_phone_us(&phone) {
            error.phone = Some("Invalid United States phone number.".to_string());
        }

        if error.first_name.is_some()
            || error.last_name.is_some()
            || error.email.is_some()
            || error.phone.is_some()
        {
            return Ok(Response::ValidationError(granite::NestedError {
                outer: "There were errors in your input.  Please correct the errors and try again."
                    .to_string(),
                inner: Some(error),
            }));
        }

        // Update the database
        granite::pg_execute!(
            db = dbcx;
            args = {
                $api_crs_user_uuid: &input.api_crs_user_uuid,
                $first_name: &first_name,
                $last_name: &last_name,
                $email: &email,
                $phone: &phone,
            };
            UPDATE
                api_crs.user
            SET
                first_name = $first_name,
                last_name = $last_name,
                email = $email,
                phone = $phone
            WHERE
                api_crs_user_uuid = $api_crs_user_uuid
        )
        .await?;

        Ok(Response::Output(Output {
            first_name,
            last_name,
            email,
            phone,
        }))
    }
}

#[approck::api]
pub mod create_processing {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub api_crs_user_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub crs_user_uuid: Uuid,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity.user_write(&dbcx, input.api_crs_user_uuid).await {
            return_authorization_error!("identity.user_write({})", input.api_crs_user_uuid);
        }

        let not_created_user = match crate::core::user::load(&dbcx, input.api_crs_user_uuid).await?
        {
            crate::core::user::User::NotCreated(not_created) => not_created,
            _ => {
                return Err(granite::Error::new(granite::ErrorType::Unexpected)
                    .add_context(format!("user_uuid: {}", input.api_crs_user_uuid))
                    .set_external_message(
                        "User is not in the correct state for create processing".to_string(),
                    ));
            }
        };

        let created_user = not_created_user.to_created(app).await?;

        Ok(Output {
            crs_user_uuid: created_user.crs_user_uuid,
        })
    }
}

#[approck::api]
pub mod verify_form_get {
    /*
    ● Happy Path
    SSN: ***********
    Mobile: ************

    ● Identity Failure (DIT)
    SSN: ***********
    Mobile: ************

    ● Identity Failure (DIT Service Outage)
    SSN: ***********
    Mobile: ************

    ● Identity Failure (SMFA Verify failure)
    SSN: ***********
    Mobile: ************

    ● Identity Failure (SMFA Send Failure)
    SSN: ***********
    Mobile: ************

    ● Identity Failure (SMFA Service Outage - Send)
    SSN: ***********
    Mobile: ************

    ● Identity Failure (SMFA Service Outage - Verify)
    SSN: ***********
    Mobile: ************

    ● Identity Failure (SMFA Incomplete)
    SSN: ***********
    Mobile: ************

    ● Identity Failure (Enrollment Failure)
    SSN: ***********
    Mobile: ************

    ● Identity Failure (ThinFile User)
    SSN: ***********
    Mobile: ************
    */

    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub api_crs_user_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub form_html: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity.user_read(&dbcx, input.api_crs_user_uuid).await {
            return_authorization_error!("identity.user_read({})", input.api_crs_user_uuid);
        }

        let is_development = app.api_crs_module().is_development();

        if !matches!(
            crate::core::user::load(&dbcx, input.api_crs_user_uuid).await?,
            crate::core::user::User::Created(_)
        ) {
            return Err(granite::Error::new(granite::ErrorType::Unexpected)
                .add_context(format!("user_uuid: {}", input.api_crs_user_uuid))
                .set_external_message(
                    "User is not in the correct state for verify form".to_string(),
                ));
        }

        let mut user_data = app
            .get_crs_user_data(&dbcx, input.api_crs_user_uuid)
            .await?;

        user_data.zip = user_data
            .zip
            .as_deref()
            .map(|z| z.chars().filter(|c| c.is_ascii_digit()).take(5).collect());

        let form_html = maud::html! {
            form method="dialog" {
                error { }
                grid-12 {
                    cell-6 {
                        grid-2 {
                            (bux::input::text::string::name_label_value("first_name", "First Name", None))
                            (bux::input::text::string::name_label_value("last_name", "Last Name", None))
                            (bux::input::text::string::name_label_value("street1", "Street Address:", user_data.address1.as_deref()))
                            (bux::input::text::string::name_label_value("street2", "Street Address 2:", user_data.address2.as_deref()))
                        }
                        grid-3 {
                            (bux::input::text::string::name_label_value("city", "City:", user_data.city.as_deref()))
                            (addr_iso::input::address_us_select::us_state_select(app, "state", "State:", user_data.state.as_deref()).await?)
                            (bux::input::text::string::name_label_value_pattern("zip", "ZIP Code:", user_data.zip.as_deref(), Some("XXXXX"), "[0-9]{5}"))
                        }
                    }
                    cell-6 {
                        grid-2 {
                            (bux::input::date::bux_input_date("birth_date", "Date of Birth:", user_data.birth_date))
                            (bux::input::select::gender::gender_select("gender", "Gender:", user_data.gender.as_deref()))
                            (bux::input::text::string::name_label_value("email", "Email", None))
                            (bux::input::text::string::name_label_value("phone", "Phone", None))
                        }
                        (bux::input::text::string::name_label_value_pattern_help("ssn", "SSN (xxx-xx-xxxx):", None, Some("XXX-XX-XXXX"), "[0-9]{3}-[0-9]{2}-[0-9]{4}","Your SSN is not retained after the credit report connection is made."))

                    }
                }

                @if is_development {
                    hr;
                    p {
                        b { "Development Data Sets: " }
                        "Use one of the following buttons to preload the form with valid test data for the condition you wish to test."
                    }
                    div id="api-crs-dds" {
                        button name="happy-path" mobile="************" ssn="***********" { "Happy Path" }
                        button name="identity-failure" mobile="************" ssn="***********" { "Identity Failure" }
                        button name="identity-failure-dit-service-outage" mobile="************" ssn="***********" { "Identity Failure (DIT Service Outage)" }
                        button name="identity-failure-smfa-verify-failure" mobile="************" ssn="***********" { "Identity Failure (SMFA Verify failure)" }
                        button name="identity-failure-smfa-send-failure" mobile="************" ssn="***********" { "Identity Failure (SMFA Send Failure)" }
                        button name="identity-failure-smfa-service-outage-send" mobile="************" ssn="***********" { "Identity Failure (SMFA Service Outage - Send)" }
                        button name="identity-failure-smfa-service-outage-verify" mobile="************" ssn="***********" { "Identity Failure (SMFA Service Outage - Verify)" }
                        button name="identity-failure-smfa-incomplete" mobile="************" ssn="***********" { "Identity Failure (SMFA Incomplete)" }
                        button name="identity-failure-enrollment-failure" mobile="************" ssn="***********" { "Identity Failure (Enrollment Failure)" }
                        button name="identity-failure-thinfile-user" mobile="************" ssn="***********" { "Identity Failure (ThinFile User)" }
                    }
                }

                hr;

                x-authorization {
                    p {
                        r###"You understand that by clicking "Continue", you are explicitly agreeing to and providing "written instructions"
                        to StitchCredit under the Fair Credit Reporting Act to obtain your credit information from one or more of the
                        three nationwide credit reporting agencies. Third-party sources, including your mobile carrier may be used to
                        verify your identity. You authorize StitchCredit to obtain such information for you to confirm your identity,
                        and, for as long as you are a member of StitchCredit, to provide you with your credit information. You may
                        elect to terminate your account and this authorization at any time."###
                    }
                }

                div style="text-align: center;" {
                    button.primary type="submit" { "Continue" }
                }
            }
        };

        Ok(Output {
            form_html: form_html.into(),
        })
    }
}

mod verify_processing {
    #[granite::gtype(TsType, TsPartial, TsError, TsPartialValidate)]
    pub struct ToVerifyProcessing {
        pub is_development: bool,
        pub api_crs_user_uuid: Uuid,
        pub crs_user_uuid: Uuid,
        pub first_name: String,
        pub last_name: String,
        pub email: String,
        pub phone: String,
        pub birth_date: DateUtc,
        pub gender: String,
        pub ssn: String,
        pub street1: String,
        pub street2: Option<String>,
        pub city: String,
        pub state: String,
        pub zip: String,
    }
}

// this mod should return the preauth token for the user
#[approck::api]
pub mod direct_get_preauth {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub api_crs_user_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub crs_user_uuid: Uuid,
        pub crs_preauth_token: String,
        pub expire_ts: DateTimeUtc,
        pub crs_api_base_url: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;
        let mut redis = app.redis_dbcx().await?;

        // Check permissions first
        if !identity.user_read(&dbcx, input.api_crs_user_uuid).await {
            return_authorization_error!("Insufficient permissions to access CRS for this user");
        }

        let api_crs = app.api_crs_module();
        let base_url = api_crs.get_base_url().to_string();

        // Load current user state
        let user = crate::core::user::load(&dbcx, input.api_crs_user_uuid).await?;

        let server_token = api_crs.get_server_token(&mut redis).await?;

        match user {
            crate::core::user::User::Created(created_user) => {
                // Get preauth token
                let preauth_output =
                    crate::core::crs::direct_preauth_token::call_direct_preauth_token(
                        crate::core::crs::direct_preauth_token::Input {
                            base_url,
                            server_token: server_token.to_string(),
                            user_id: created_user.crs_user_uuid,
                        },
                    )
                    .await?;

                Ok(Output {
                    crs_user_uuid: preauth_output.crs_user_uuid,
                    crs_preauth_token: preauth_output.token,
                    expire_ts: preauth_output.expire_ts,
                    crs_api_base_url: api_crs.get_base_url().to_string(),
                })
            }
            _ => Err(granite::Error::new(granite::ErrorType::InvalidOperation)
                .add_context(format!("api_crs_user_uuid: {}", input.api_crs_user_uuid))
                .set_external_message("User is not in the correct state for preauth".to_string())),
        }
    }
}

#[approck::api]
pub mod set_verified {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub api_crs_user_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output;

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity.user_write(&dbcx, input.api_crs_user_uuid).await {
            return_authorization_error!("identity.user_write({})", input.api_crs_user_uuid);
        }

        let created_user = match crate::core::user::load(&dbcx, input.api_crs_user_uuid).await? {
            crate::core::user::User::Created(created_user) => created_user,
            _ => {
                return Err(granite::Error::new(granite::ErrorType::InvalidOperation)
                    .add_context(format!("api_crs_user_uuid: {}", input.api_crs_user_uuid))
                    .set_external_message(
                        "User is not in the correct state for verification".to_string(),
                    ));
            }
        };

        created_user.to_verified(app).await?;

        Ok(Output {})
    }
}

#[approck::api]
pub mod get_latest_report {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub api_crs_user_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub last_report_ts: DateTimeUtc,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity.user_write(&dbcx, input.api_crs_user_uuid).await {
            return_authorization_error!("identity.user_write({})", input.api_crs_user_uuid);
        }

        let user = crate::core::user::load(&dbcx, input.api_crs_user_uuid).await?;

        let with_report = match user {
            crate::core::user::User::Verified(verified) => verified.to_with_report(app).await?,
            crate::core::user::User::WithReport(with_report) => with_report,
            _ => {
                return Err(granite::Error::new(granite::ErrorType::InvalidOperation)
                    .add_context(format!("api_crs_user_uuid: {}", input.api_crs_user_uuid))
                    .set_external_message(
                        "User is not in the correct state for fetching latest report".to_string(),
                    ));
            }
        };

        Ok(Output {
            last_report_ts: with_report.last_report_ts,
        })
    }
}
