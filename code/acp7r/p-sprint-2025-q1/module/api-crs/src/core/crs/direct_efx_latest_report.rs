use granite::ResultExt;

#[derive(serde::Serialize)]
pub struct Input {
    pub base_url: String,
    pub server_token: super::types::CrsServerToken,
    pub api_crs_user_uuid: granite::Uuid,
}

pub async fn call_direct_efx_latest_report(
    app: &impl crate::App,
    input: Input,
) -> granite::Result<String> {
    let dbcx = app.postgres_dbcx().await?;

    let is_development = app.api_crs_module().is_development();

    // load the user
    let user = crate::core::user::load(&dbcx, input.api_crs_user_uuid).await?;

    let crs_user_uuid = match user {
        crate::core::user::User::Verified(verified) => verified.crs_user_uuid,
        crate::core::user::User::WithReport(with_report) => with_report.crs_user_uuid,
        _ => {
            return Err(granite::Error::new(granite::ErrorType::InvalidOperation)
                .add_context(format!("api_crs_user_uuid: {}", input.api_crs_user_uuid))
                .set_external_message(
                    "User is not in the correct state for fetching latest report".to_string(),
                ));
        }
    };

    let endpoint_uri = format!(
        "{}/direct/efx-latest-report/{}",
        input.base_url, crs_user_uuid
    );

    let client = reqwest::Client::new();

    let response = client
        .get(endpoint_uri.to_string())
        .header("Authorization", format!("Bearer {}", input.server_token))
        .header("Content-Type", "application/json")
        .header("Accept", "application/json")
        .send()
        .await
        .amend(|e| {
            e.add_context("sending request")
                .add_context(format!("url: {endpoint_uri}"))
                .set_external_message("Failed to send request to API.".to_string())
        })?;

    let status = response.status();

    let text = if is_development {
        include_str!("direct_efx_latest_report.json").to_string()
    } else {
        // Failure branch
        if !status.is_success() {
            let text = response.text().await.amend(|e| {
                e.add_context("reading response text")
                    .add_context(format!("url: {endpoint_uri}"))
                    .add_context(format!("status: {status}"))
                    .set_external_message("Failed to read response text from API call.".to_string())
            })?;

            let error = serde_json::from_str::<super::types::CrsApiError>(&text).amend(|e| {
                e.add_context("decoding response text")
                    .add_context(format!("url: {endpoint_uri}"))
                    .add_context(format!("status: {status}"))
                    .add_context(format!("text: {text}"))
                    .set_external_message(
                        "Failed to decode response text from API call.".to_string(),
                    )
            })?;

            return Err(
                granite::Error::new(granite::ErrorType::ApiRequest).add_context(
                    super::types::Error::BadRequest {
                        error: error.messages.join(", "),
                    },
                ),
            );
        }

        // success branch
        response.text().await.amend(|e| {
            e.add_context("reading response text")
                .add_context(format!("url: {endpoint_uri}"))
                .add_context(format!("status: {status}"))
                .set_external_message("Failed to read response text from API call.".to_string())
        })?
    };

    Ok(text)
}
