use granite::ResultExt;

#[derive(serde::Serialize)]
pub struct Input {
    pub base_url: String,
    pub refresh_token: String,
}

#[derive(serde::Deserialize)]
struct ApiOutput {
    pub token: String,
    pub expire: i64,
    pub refresh: String,
}

#[derive(serde::Deserialize)]
pub struct Output {
    pub token: String,
    pub expire_ts: granite::DateTimeUtc,
    pub refresh: String,
}

pub async fn direct_refresh_token(input: Input) -> granite::Result<Output> {
    let endpoint_uri = format!(
        "{}/direct/refresh-token?token={}",
        input.base_url, input.refresh_token
    );

    let client = reqwest::Client::new();

    let response = client
        .get(endpoint_uri.to_string())
        .header("Authorization", format!("Bearer {}", input.refresh_token))
        .header("Content-Type", "application/json")
        .header("Accept", "application/json")
        .send()
        .await
        .amend(|e| {
            e.add_context("sending request")
                .add_context(format!("url: {endpoint_uri}"))
                .set_external_message("Failed to send request to API.".to_string())
        })?;

    let status = response.status();

    // Failure branch
    if !status.is_success() {
        let text = response.text().await.amend(|e| {
            e.add_context("reading response text")
                .add_context(format!("url: {endpoint_uri}"))
                .add_context(format!("status: {status}"))
                .set_external_message("Failed to read response text from API call.".to_string())
        })?;

        let error = serde_json::from_str::<super::types::CrsApiError>(&text).amend(|e| {
            e.add_context("decoding response text")
                .add_context(format!("url: {endpoint_uri}"))
                .add_context(format!("status: {status}"))
                .add_context(format!("text: {text}"))
                .set_external_message("Failed to decode response text from API call.".to_string())
        })?;

        // Check for specific error codes that need special handling
        if error
            .codes
            .contains(&super::types::CrsApiErrorCode::UserAlreadyRegistered)
        {
            return Err(granite::Error::new(granite::ErrorType::ApiRequest)
                .add_context(format!("url: {endpoint_uri}"))
                .add_context(format!("status: {status}"))
                .add_context(format!("text: {text}"))
                .set_external_message("User is already registered".to_string()));
        } else {
            return Err(granite::Error::new(granite::ErrorType::ApiRequest)
                .add_context(format!("url: {endpoint_uri}"))
                .add_context(format!("status: {status}"))
                .add_context(format!("text: {text}"))
                .set_external_message(error.messages.join(", ")));
        }
    }

    // success branch
    let text = response.text().await.amend(|e| {
        e.add_context("reading response text")
            .add_context(format!("url: {endpoint_uri}"))
            .add_context(format!("status: {status}"))
            .set_external_message("Failed to read response text from API call.".to_string())
    })?;

    let output = serde_json::from_str::<ApiOutput>(&text).amend(|e| {
        e.add_context("decoding response text")
            .add_context(format!("url: {endpoint_uri}"))
            .add_context(format!("status: {status}"))
            .add_context(format!("text: {text}"))
            .set_external_message("Failed to decode response text from API call.".to_string())
    })?;

    Ok(Output {
        token: output.token,
        expire_ts: granite::Utc::now() + granite::Duration::seconds(output.expire),
        refresh: output.refresh,
    })
}
