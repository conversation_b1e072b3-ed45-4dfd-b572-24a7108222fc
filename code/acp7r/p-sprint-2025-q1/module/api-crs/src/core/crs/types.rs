use std::ops::Deref;

#[granite::gtype(ApiOutput)]
pub enum CrsUserState {
    NotCreated,
    Created,
    Verified,
}

// ---------------------------------------------------------------------------------------------------------

#[derive(Debug)]
pub enum Error {
    RequestError { url: String, error: String },
    ResponseError { url: String, error: String },
    DecodingError { input: String, error: String },
    BadRequest { error: String },
    OtherError(String),
}

impl std::fmt::Display for Error {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{self:?}")
    }
}

impl std::error::Error for Error {}

// ---------------------------------------------------------------------------------------------------------

#[derive(serde::Serialize)]
pub struct CrsServerToken(String);

impl Deref for CrsServerToken {
    type Target = String;

    fn deref(&self) -> &Self::Target {
        &self.0
    }
}

impl From<String> for CrsServerToken {
    fn from(s: String) -> Self {
        Self(s)
    }
}

impl std::fmt::Display for CrsServerToken {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.0)
    }
}

// ---------------------------------------------------------------------------------------------------------

#[derive(serde::Deserialize, serde::Serialize, Debug)]
pub struct CrsApiError {
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub codes: Vec<CrsApiErrorCode>,
    pub messages: Vec<String>,
    pub details: Vec<Option<String>>,
}

impl std::fmt::Display for CrsApiError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{self:?}",)
    }
}

impl std::error::Error for CrsApiError {}

#[derive(serde::Deserialize, serde::Serialize, Debug, PartialEq, Eq, Clone)]
pub enum CrsApiErrorCode {
    #[serde(rename = "SC101")]
    UserNotFound,
    #[serde(rename = "SC102")]
    UserAlreadyRegistered,
    #[serde(rename = "SC103")]
    InvalidToken,
    #[serde(rename = "SC104")]
    UserMismatch,
    #[serde(rename = "SC105")]
    RecoveryAnswerFailed,
    #[serde(rename = "SC106")]
    HostTokenExpired,
    #[serde(rename = "SC107")]
    UserTokenExpired,
    #[serde(rename = "SC108")]
    ActionTokenExpired,
    #[serde(rename = "SC109")]
    PasswordTokenExpired,
    #[serde(rename = "SC110")]
    RefreshTokenExpired,
    #[serde(rename = "SC111")]
    RefreshTokenPremature,
    #[serde(rename = "SC112")]
    MobileTokenExpired,
    #[serde(rename = "SC113")]
    LoginFailure,
    #[serde(rename = "SC114")]
    AccountTemporarilyLockedOut,
    #[serde(rename = "SC115")]
    IPTemporarilyLockedOut,
    #[serde(rename = "SC116")]
    UserDisabled,
    #[serde(rename = "SC117")]
    PreauthTokenExpired,
    #[serde(rename = "SC120")]
    UserException,
    #[serde(rename = "SC121")]
    UserAccountClosed,
    #[serde(rename = "SC301")]
    UserAlreadyIdentified,
    #[serde(rename = "SC302")]
    UserIdentityRequired,
    #[serde(rename = "SC303")]
    UserIdentificationFailed,
    #[serde(rename = "SC304")]
    PhoneNumberRequired,
    #[serde(rename = "SC305")]
    OTPFailed,
    #[serde(rename = "SC306")]
    ThinFileError,
    #[serde(rename = "SC307")]
    UnenrollmentError,
    #[serde(rename = "SC308")]
    FeatureEnrollmentError,
    #[serde(rename = "SC309")]
    MobileAuthorizationRequired,
    #[serde(rename = "SC310")]
    IDAlreadyStored,
    #[serde(rename = "SC311")]
    IDFSError,
    #[serde(rename = "SC312")]
    InvalidFlagException,
    #[serde(rename = "SC313")]
    EnrollmentError,
    #[serde(rename = "SC314")]
    MobileVerificationFailed,
    #[serde(rename = "SC315")]
    IDFSUnavailable,
    #[serde(rename = "SC316")]
    IDFSRateLimitException,
    #[serde(rename = "SC317")]
    IDFSDataException,
    #[serde(rename = "SC318")]
    UserEnrollmentLimitExceeded,
    #[serde(rename = "SC319")]
    IDFSFault,
    #[serde(rename = "SC320")]
    VS3Unavailable,
    #[serde(rename = "SC321")]
    IDFSUserLockedWait72hrs,
    #[serde(rename = "SC322")]
    IDFSRequestAlreadyComplete,
    #[serde(rename = "SC323")]
    SMFAFailedToSendLink,
    #[serde(rename = "SC324")]
    SMFAStatusFailed,
    #[serde(rename = "SC325")]
    SMFAStatusIncomplete,
    #[serde(rename = "SC326")]
    SMFATokenInvalidOrExpired,
    #[serde(rename = "SC327")]
    RequestTooEarly,
    #[serde(rename = "SC328")]
    RequestTimedOut,
    #[serde(rename = "SC350")]
    UnauthorizedIdentityMethod,
    #[serde(rename = "SC401")]
    DirectTokenExpired,
    #[serde(rename = "SC402")]
    UnauthorizedConsumerAccess,
    #[serde(rename = "SC403")]
    LoginFailure2,
    #[serde(rename = "SC405")]
    DataAccessRequired,
    #[serde(rename = "SC406")]
    AlertNotFound,
    #[serde(rename = "SC407")]
    UnauthorizedPremiumFeatureAccess,
}

impl CrsApiErrorCode {
    pub fn get_message(&self) -> &'static str {
        match self {
            Self::UserNotFound => "User Not Found",
            Self::UserAlreadyRegistered => "User Already Registered",
            Self::InvalidToken => "Invalid Token",
            Self::UserMismatch => "User Mismatch",
            Self::RecoveryAnswerFailed => "Recovery Answer Failed",
            Self::HostTokenExpired => "Host Token Expired",
            Self::UserTokenExpired => "User Token Expired",
            Self::ActionTokenExpired => "Action Token Expired",
            Self::PasswordTokenExpired => "Password Token Expired",
            Self::RefreshTokenExpired => "Refresh Token Expired",
            Self::RefreshTokenPremature => "Refresh Token Premature",
            Self::MobileTokenExpired => "Mobile Token Expired",
            Self::LoginFailure => "Login Failure",
            Self::AccountTemporarilyLockedOut => "Account Temporarily Locked Out",
            Self::IPTemporarilyLockedOut => "IP Temporarily Locked Out",
            Self::UserDisabled => "User Disabled",
            Self::PreauthTokenExpired => "Preauth Token Expired",
            Self::UserException => "User Exception",
            Self::UserAccountClosed => "User Account Closed",
            Self::UserAlreadyIdentified => "User Already Identified",
            Self::UserIdentityRequired => "User Identity Required",
            Self::UserIdentificationFailed => "User Identification Failed",
            Self::PhoneNumberRequired => "Phone Number Required",
            Self::OTPFailed => "OTP Failed",
            Self::ThinFileError => "Thin File Error",
            Self::UnenrollmentError => "Unenrollment Error",
            Self::FeatureEnrollmentError => "Feature Enrollment Error",
            Self::MobileAuthorizationRequired => "Mobile authorization required",
            Self::IDAlreadyStored => "ID Already Stored",
            Self::IDFSError => "IDFS Error",
            Self::InvalidFlagException => "Invalid Flag Exception",
            Self::EnrollmentError => "Enrollment Error",
            Self::MobileVerificationFailed => "Mobile Verification Failed",
            Self::IDFSUnavailable => "IDFS Unavailable",
            Self::IDFSRateLimitException => "IDFS Rate Limit Exception",
            Self::IDFSDataException => "IDFS Data Exception",
            Self::UserEnrollmentLimitExceeded => "User Enrollment Limit Exceeded",
            Self::IDFSFault => "IDFS Fault",
            Self::VS3Unavailable => "VS3 Unavailable",
            Self::IDFSUserLockedWait72hrs => "IDFS User Locked Wait 72hrs",
            Self::IDFSRequestAlreadyComplete => "IDFS Request Already Complete",
            Self::SMFAFailedToSendLink => "SMFA Failed to Send Link",
            Self::SMFAStatusFailed => "SMFA Status Failed",
            Self::SMFAStatusIncomplete => "SMFA Status Incomplete",
            Self::SMFATokenInvalidOrExpired => "SMFA Token Invalid or Expired",
            Self::RequestTooEarly => "Request Too Early",
            Self::RequestTimedOut => "Request Timed Out",
            Self::UnauthorizedIdentityMethod => "Unauthorized Identity Method",
            Self::DirectTokenExpired => "Direct Token Expired",
            Self::UnauthorizedConsumerAccess => "Unauthorized Consumer Access",
            Self::LoginFailure2 => "Login Failure",
            Self::DataAccessRequired => "Data Access Required",
            Self::AlertNotFound => "Alert Not Found",
            Self::UnauthorizedPremiumFeatureAccess => "Unauthorized Premium Feature Access",
        }
    }
}

impl std::fmt::Display for CrsApiErrorCode {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.get_message())
    }
}

// ---------------------------------------------------------------------------------------------------------

#[granite::gtype]
pub struct CrsUserAuthPayload {
    pub crs_user_uuid: Uuid,
    pub token: String,
    pub expire_ts: DateTimeUtc,
}

// ---------------------------------------------------------------------------------------------------------

/// Data passed when user is created in CRS
#[derive(Debug)]
#[granite::gtype]
pub struct UserCreatedData {
    pub api_crs_user_uuid: Uuid,
    pub crs_user_uuid: Uuid,
}

/// Data passed when user is verified in CRS
#[derive(Debug)]
#[granite::gtype]
pub struct UserVerifiedData {
    pub api_crs_user_uuid: Uuid,
    pub report_data: Option<String>, // JSON string of report data
}

// ---------------------------------------------------------------------------------------------------------

pub struct CrsUserReport {}

// ---------------------------------------------------------------------------------------------------------
// These types are here strictly for the benefit of the Typescript client. They are not used in the Rust code or for actual encoding/decoding.
// They are used for interacting with the /users/* api endpoints which are for front end client-side use only.

#[granite::gtype(TsAll)]
pub struct CrsUserTokenResponse {
    id: String,
    email: String,
    fname: String,
    lname: String,
    idpass: bool,
    createdAt: i64,
    updatedAt: i64,
    smsMsg: bool,
    emailMsg: bool,
    pushMsg: bool,
    flags: i64,
    justEnrolled: bool,
    token: String,
    expires: i64,
    expires_ts: i64,
    refresh: String,
}

#[granite::gtype(TsAll)]
pub struct CrsUserVerificationData {
    pub fname: String,
    pub lname: String,
    pub mobile: String,
    pub email: String,
    pub dob: String,
    pub gender: String,
    pub ssn: String,
    pub street1: String,
    pub street2: Option<String>,
    pub city: String,
    pub state: String,
    pub zip: String,
}

#[granite::gtype(TsAll)]
pub struct CrsUserIdentityResponse {
    mobile: String,
    token: String,
    expires: DateTimeUtc,
}

#[granite::gtype(TsAll)]
pub struct CrsSMFASendLinkResponse {
    pub linkUrl: String,
    pub smsMessage: String,
    pub token: String,
    pub expires: DateTimeUtc,
}

#[granite::gtype(TsAll)]
pub struct CrsSMFAVerifyStatusResponse {
    pub id: String,
    pub email: String,
    pub fname: String,
    pub lname: String,
    pub idpass: bool,
    pub createdAt: i64,
    pub updatedAt: i64,
    pub smsMessage: bool,
    pub emailMessage: bool,
    pub pushMessage: bool,
    pub flags: i64,
    pub just_enrolled: bool,
}

#[granite::gtype(TsAll)]
pub struct CrsErrorResponse {
    timestamp: i64,
    codes: Vec<String>,
    messages: Vec<String>,
    details: Vec<String>,
}
