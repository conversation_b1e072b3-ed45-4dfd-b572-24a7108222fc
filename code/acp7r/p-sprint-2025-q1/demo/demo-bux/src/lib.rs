#[path = "libλ.rs"]
pub mod libλ;
mod module;
mod web;

pub trait App: approck::App + approck::server::App {}
pub trait Identity {
    fn web_usage(&self) -> bool {
        true
    }
    fn api_usage(&self) -> bool {
        true
    }
}

pub trait Document: bux::document::Base {}

///////////////////////////////////////////////////////////////////////////////////////////////////

#[derive(serde::Deserialize)]
pub struct AppConfig {
    pub webserver: approck::server::ModuleConfig,
}

pub struct AppStruct {
    pub webserver: approck::server::Module,
}

#[derive(Debug)]
pub struct IdentityStruct {}

bux::document! {
    pub struct DocumentStruct {}

    impl bux::document::Base for Document {
        fn render_body(&self) -> maud::Markup {
            use bux::document::{Base, Nav1, Nav2, <PERSON><PERSON>av, FooterSocial, FooterLinks};
            use maud::{html, PreEscaped};
            html!(
                layout-wrapper-outer {
                    layout-wrapper-inner {
                        header-bar.disabled id="header-bar" {}
                        nav-wrapper {
                            content-container {
                                nav-header id="horizontal-nav-header" {
                                    a href="/" aria-label="Home - {Company Name}" {
                                        img src="https://appcove.com/assets/appcove_logo_2.png" alt="{Company Name} Logo" {}
                                    }
                                }
                                (Nav1::render_nav1(self))
                                (Nav2::render(self))
                            }
                        }
                        (PageNav::render(self))
                        content-container {
                            (Base::render_body_inner(self))
                        }
                    }
                    footer {
                        (FooterSocial::render(self))
                        (FooterLinks::render(self))
                        p id="footer-copyright" {
                            small {
                                "Copyright "
                                (PreEscaped("&copy;"))
                                " 2024 {Company Name}. All rights reserved."
                            }
                        }
                    }
                }
            )
        }
    }

    impl bux::document::Nav1 for DocumentStruct {}
    impl bux::document::Nav2 for DocumentStruct {}
    impl bux::document::PageNav for DocumentStruct {}
    impl bux::document::FooterSocial for DocumentStruct {}
    impl bux::document::FooterLinks for DocumentStruct {}
}

///////////////////////////////////////////////////////////////////////////////////////////////////

impl approck::App for AppStruct {
    type Config = AppConfig;
    type Identity = IdentityStruct;
    fn new(config: Self::Config) -> granite::Result<Self> {
        use approck::Module;
        Ok(Self {
            webserver: approck::server::Module::new(config.webserver)?,
        })
    }
    async fn init(&self) -> granite::Result<()> {
        use approck::Module;
        self.webserver.init().await?;
        // get the crate name using the env! macro
        let crate_name = env!("CARGO_PKG_NAME");
        println!("init: {crate_name}");
        Ok(())
    }

    async fn auth(&self, _req: &approck::server::Request) -> granite::Result<IdentityStruct> {
        Ok(IdentityStruct {})
    }
}

impl App for AppStruct {}
impl Identity for IdentityStruct {}
impl Document for DocumentStruct {}
